# 广西就业门户网站

> 广西壮族自治区数智人社多城市门户网站前端项目

## 项目简介

这是一个支持多城市的门户网站系统，基于 Vue 3 + TypeScript + Vite 构建。支持广西全区14个城市的个性化门户展示，提供政务服务、民生服务、营商服务等功能。

### 核心特性

- **多城市架构**: 支持14个广西城市的独立门户配置
- **动态路由**: `/:city/page` 格式，灵活的城市切换体验
- **主题定制**: 每个城市独立的主题色彩和视觉风格
- **响应式设计**: 移动端友好的自适应布局
- **类型安全**: 完整的 TypeScript 支持
- **现代化开发**: Vue 3 + Composition API + Pinia

## 快速开始

### 环境要求

- Node.js >= 16
- pnpm >= 8.0（推荐使用）

### 安装依赖

```bash
pnpm install
```

### 开发环境

```bash
pnpm dev
```

项目将在 `http://localhost:3000` 启动

### 构建生产版本

```bash
# 包含类型检查的完整构建
pnpm build

# 预览构建结果
pnpm preview
```

### 代码质量检查

```bash
# TypeScript 类型检查
pnpm type-check

# ESLint 代码检查
pnpm lint

# 自动修复 ESLint 问题
pnpm lint:fix
```

### 测试

```bash
# 运行测试
pnpm test

# 测试 UI 界面
pnpm test:ui
```

## 项目架构

### 技术栈

| 技术 | 版本 | 说明 |
|------|------|------|
| Vue | 3.4+ | 渐进式前端框架 |
| TypeScript | 5.4+ | 类型安全的 JavaScript |
| Vite | 5.0+ | 高性能构建工具 |
| Vue Router | 4.3+ | 官方路由管理器 |
| Pinia | 2.1+ | 状态管理库 |
| Element Plus | 2.7+ | Vue 3 UI 组件库 |
| UnoCSS | 0.61+ | 原子化 CSS 引擎 |
| Sass | 1.77+ | CSS 预处理器 |

### 目录结构

```
src/
├── api-services/          # API 服务层
├── assets/               # 静态资源
│   ├── iconfont/        # 图标字体
│   └── styles/          # 全局样式
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   └── layout/         # 布局组件
├── config/             # 配置文件
├── constants/          # 常量定义
├── layout/            # 页面布局
├── router/            # 路由配置
├── stores/            # Pinia 状态管理
├── types/             # TypeScript 类型定义
├── utils/             # 工具函数
└── views/             # 页面组件
    └── homepages/     # 各城市首页
        ├── gx/   # 广西首页
        ├── gl/        # 桂林首页
        └── ...        # 其他城市
```

## 多城市架构

### 支持的城市

项目支持以下14个广西城市:

| 城市代码 | 城市名称 | 路径示例 | 特色服务 |
|---------|---------|----------|---------|
| gx | 广西 | `/` | 自治区政务、招商引资、文旅服务 |
| gl | 桂林 | `/gl` | 政务服务、文旅服务、民生服务 |
| lz | 柳州 | `/lz` | 政务服务、工业服务、营商服务 |
| wz | 梧州 | `/wz` | 政务服务、两广合作、民生服务 |
| bs | 百色 | `/bs` | 政务服务、红色文旅、营商服务 |
| qz | 钦州 | `/qz` | 政务服务、港口服务、营商服务 |
| hc | 河池 | `/hc` | 政务服务、长寿文化、民生服务 |
| bh | 北海 | `/bh` | 政务服务、滨海旅游、营商服务 |
| fg | 防港 | `/fg` | 政务服务、边贸服务、营商服务 |
| yl | 玉林 | `/yl` | 政务服务、商贸服务、营商服务 |
| cz | 崇左 | `/cz` | 政务服务、边贸服务、营商服务 |
| gg | 贵港 | `/gg` | 政务服务、港口服务、营商服务 |
| lb | 来宾 | `/lb` | 政务服务、瑶族文化、营商服务 |
| hz | 贺州 | `/hz` | 政务服务、康养旅游、营商服务 |
| nn | 南宁 | `/nn` | 政务服务、民生服务、便民查询 |

### 路由设计

- **广西自治区**: 使用根路径 `/`
- **其他城市**: 使用缩写路径 `/{cityCode}`
- **页面路由**: `/{cityCode}/{page}` 或 `/{page}`（广西）

### 城市切换

```typescript
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()

// 切换到指定城市
cityStore.setCurrentCity('gl') // 切换到桂林

// 获取当前城市信息
console.log(cityStore.cityInfo)

// 生成城市路径
const homePath = cityStore.getCityHomePath // 当前城市首页路径
const pagePath = cityStore.getCityPagePath('about') // 当前城市页面路径
```


## 组件系统

### 核心组件

#### 布局组件
- **AppLayout**: 应用主布局
- **HeaderComponent**: 页面头部
- **TopBar**: 顶部导航栏
- **CityFooter**: 页面底部

#### 功能组件
- **CitySelector**: 城市选择器
- **SearchWrapper**: 搜索组件

### 组件开发规范

```vue
<!-- PascalCase 文件名: MyComponent.vue -->
<template>
  <div class="my-component">
    <!-- 使用 UnoCSS 原子化类名 -->
  </div>
</template>

<script setup lang="ts">
// 使用 Composition API + TypeScript
import { useCityStore } from '@/stores/cityStore'

// Props 定义
interface Props {
  title?: string
}

defineProps<Props>()

const cityStore = useCityStore()
</script>

<style scoped lang="scss">
// 组件私有样式
.my-component {
  // 样式规则
}
</style>
```

## 开发指南

### 添加新城市

1. **更新城市配置**
   在 `src/stores/cityStore.ts` 的 `CITIES_CONFIG` 中添加新城市配置

2. **创建城市首页**
   在 `src/views/homepages/{cityCode}/` 目录下创建 `HomePage.vue`

3. **更新路由配置**
   在 `src/router/index.ts` 的路由匹配模式中添加新城市代码

4. **测试城市切换**
   确保新城市的路由、主题、组件加载正常工作

### 样式开发

#### UnoCSS 原子化
```vue
<template>
  <!-- 推荐使用原子化类名 -->
  <div class="flex justify-center items-center w-full h-screen bg-blue-500">
    <h1 class="text-2xl font-bold text-white">标题</h1>
  </div>
</template>
```

#### Sass 变量和混合
```scss
// src/assets/styles/main.scss
:root {
  --color-primary: #0ea5e9;
  --color-text: #333;
  --border-radius: 8px;
}

.custom-component {
  color: var(--color-primary);
  border-radius: var(--border-radius);
}
```

### API 集成

项目使用 Swagger 自动生成的 TypeScript API 客户端，提供完整的类型安全和接口文档。

#### 基础配置

```typescript
// src/utils/axios-utils.ts
import { Configuration } from '@/api-services'
import { getPcAPI } from '@/utils/axios-utils'

// 创建配置实例
export const serveConfig = new Configuration({
    basePath: import.meta.env.VITE_API_URL
});

// 获取 API 客户端实例
import { PositionApi } from '@/api-services'
const positionApi = getPcAPI(PositionApi);
```

#### 核心接口示例

##### 1. 职位搜索配置
```typescript
// GET /api/Position/SearchOption
// 获取职位搜索的筛选配置项
const searchOptions = await positionApi.apiPositionSearchOptionGet({
  districtId: BussDistrict.Guilin,     // 请求地市
  from: ApplicationPlatform.Web        // 请求来源
});
```

#### 错误处理和拦截器

```typescript
// src/utils/axios-utils.ts 已配置完整的请求/响应拦截器

// 自动功能：
// ✓ Token 自动管理和刷新
// ✓ 401 错误自动跳转登录
// ✓ 统一错误提示
// ✓ 请求/响应日志记录
// ✓ 标准化数据格式处理

// 使用示例（带错误处理）
try {
  const result = await positionApi.apiPositionSearchOptionGet();
  console.log('搜索配置:', result.data);
} catch (error) {
  // 错误已由拦截器自动处理并显示用户友好提示
  console.error('请求失败:', error);
}
```

#### 类型定义

所有 API 相关的类型定义都在 `src/api-services/models/` 中自动生成：

```typescript
// 导入类型定义
import { 
  BussDistrict,           // 业务地市枚举
  ApplicationPlatform,    // 应用平台枚举
  PositionRequest,        // 职位搜索请求类型
  RestfulResultSearchOptions  // 搜索配置响应类型
} from '@/api-services';

// 类型安全的请求
const request: PositionRequest = {
  keyword: '软件工程师',
  pageIndex: 1,
  pageSize: 10
};
```

### 状态管理

```typescript
// src/stores/exampleStore.ts
import { defineStore } from 'pinia'

export const useExampleStore = defineStore('example', () => {
  const data = ref([])
  const loading = ref(false)

  const fetchData = async () => {
    loading.value = true
    try {
      // 异步操作
    } finally {
      loading.value = false
    }
  }

  return {
    data: readonly(data),
    loading: readonly(loading),
    fetchData
  }
}, {
  persist: {
    key: 'example-store',
    storage: localStorage,
    paths: ['data']
  }
})
```

## 常用工具

### 路径别名

```typescript
// 已配置的路径别名
'@': 'src',
'@/components': 'src/components',
'@/views': 'src/views',
'@/stores': 'src/stores',
'@/services': 'src/api-services',
'@/utils': 'src/utils',
'@/types': 'src/types'
```

### 自动导入

项目配置了自动导入，以下 API 无需手动引入:
- Vue Composition API (`ref`, `reactive`, `computed`, `watch` 等)
- Vue Router API (`useRouter`, `useRoute` 等)
- Pinia API (`defineStore`, `storeToRefs` 等)
- Element Plus 组件
- VueUse 工具函数

## 开发流程

### 提交前检查清单

1. **类型检查**: `pnpm type-check`
2. **代码规范**: `pnpm lint`
3. **构建测试**: `pnpm build`
4. **功能测试**: `pnpm test`

### Git 提交规范

```bash
# 功能开发
git commit -m "feat: 添加桂林城市首页组件"

# 问题修复
git commit -m "fix: 修复城市切换主题不更新的问题"

# 文档更新
git commit -m "docs: 更新README开发指南"

# 样式调整
git commit -m "style: 优化首页布局样式"

# 重构代码
git commit -m "refactor: 重构城市路由逻辑"
```

## 技术支持

### 常见问题

**Q: 如何添加新的城市？**
A: 参考上方"添加新城市"章节，按步骤配置即可。

**Q: 路由跳转异常？**
A: 确认城市代码在 `CITIES_CONFIG` 中存在且 `isActive` 为 `true`。

**Q: 组件自动导入失败？**
A: 检查 `vite.config.ts` 中的 AutoImport 和 Components 插件配置。

### 注意
- 因后端尚未提供接口 请先完成页面编写