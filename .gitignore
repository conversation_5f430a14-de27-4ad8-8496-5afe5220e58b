# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建产物
dist/
dist-ssr/

# 本地环境变量
.env.local
.env.*.local

# 日志文件
logs/
*.log

# 编辑器目录
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 测试覆盖率
coverage/
*.lcov

# Nuxt.js 构建目录
.nuxt/

# 缓存目录
.cache/
.parcel-cache/

# TypeScript 构建信息
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/
.claude
# auto generated files
auto-imports.d.ts
components.d.ts

memory-bank

api_build/swagger-codegen-cli.jar