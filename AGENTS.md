# Repository Guidelines

## 项目结构与模块组织
- 源码位于 `src/`：`components/`（common、layout）、`views/`（含 `homepages/`）、`stores/`（Pinia）、`router/`、`services/`、`utils/`、`types/`，以及 `assets/styles/`（SCSS）。
- 入口文件：`main.ts`、`App.vue`；路由定义在 `src/router/index.ts`。
- 样式：使用 `src/assets/styles` 中的 SCSS 与 UnoCSS 原子类，见 `uno.config.ts`。
- 路径别名：优先使用 `@/...` 导入（在 `tsconfig.json` 与 `vite.config.ts` 中配置）。
- 更多背景：参见 `docs/front-end-architecture.md` 与 `docs/sass-migration-guide.md`。

## 构建、测试与本地开发命令
- `pnpm install`：安装依赖（本项目使用 pnpm）。
- `pnpm dev`：启动 Vite 开发服务，地址 `http://localhost:3000`（`/api` 代理到本机 `:8080`）。
- `pnpm build`：先用 `vue-tsc` 进行类型检查，再构建生产包。
- `pnpm preview`：本地预览生产构建产物。
- `pnpm test` | `pnpm test:ui`：使用 Vitest 运行测试（CLI 或 UI 模式）。
- `pnpm lint` | `pnpm lint:fix`：运行 ESLint（TS + Vue），并可自动修复。
- `pnpm type-check`：仅运行严格的 TypeScript 类型检查（不产出文件）。

## 编码风格与命名规范
- 缩进 2 个空格；启用 TypeScript 严格模式。
- ESLint：详见 `.eslintrc.js`（如优先使用 `const`、禁止重复导入；关闭 Vue 多词组件名限制）。
- Vue 组件文件名：PascalCase（如 `HeaderComponent.vue`、`CityFooter.vue`）。
- Store 文件：camelCase（如 `cityStore.ts`）；工具：kebab-case（如 `axios-utils.ts`）。
- 导入：优先使用别名路径（如 `@/components/...`）；常用 API 由 `unplugin-auto-import` 自动导入。

## 测试指南
- 框架：Vitest + Vue Test Utils。目前无测试文件，可在源码同目录或 `tests/` 下新增 `*.spec.ts`。
- 建议：覆盖关键的 store、service 与视图逻辑。使用 `pnpm test` 运行。

## 提交与 Pull Request 规范
- 提交信息：推荐 Conventional Commits（如 `feat:`、`fix:`、`chore:`），动词祈使，范围清晰。
- PR 要求：说明目的、关联 issue，UI 变更请附截图/GIF。
- 必要自检：本地通过 `pnpm lint`、`pnpm type-check`、`pnpm build`；涉及破坏性变更或环境变量请在说明中标注。

## 安全与配置提示
- 环境变量：使用 `.env.development` / `.env.production` 中的 `VITE_*`；请勿提交机密信息。
- 接口代理：开发环境在 `vite.config.ts` 中将 `/api` 代理到 `http://localhost:8080`。
- 资源与性能：全局样式放于 `src/assets/styles`；大型视图优先路由级按需加载。
