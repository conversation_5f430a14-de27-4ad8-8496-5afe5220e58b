---
description: Styling guidelines, UnoCSS usage patterns and responsive design
globs: **/*.vue,**/*.scss,uno.config.ts
---

# 样式开发指南

## 样式架构

### 1. 样式优先级

1. **UnoCSS 原子化类名** - 首选，快速开发
2. **Element Plus 组件样式** - UI 组件默认样式
3. **Sass 自定义样式** - 复杂样式和主题定制
4. **CSS 变量** - 主题切换和动态样式

### 2. 核心配置文件

- [uno.config.ts](mdc:uno.config.ts) - UnoCSS 配置和自定义规则
- [src/assets/styles/main.scss](mdc:src/assets/styles/main.scss) - 全局样式
- [vite.config.ts](mdc:vite.config.ts) - Sass 编译配置

## UnoCSS 原子化开发

### 1. 布局和定位

```vue
<template>
  <!-- Flexbox 布局 -->
  <div class="flex items-center justify-between">
    <div class="flex-1">内容区域</div>
    <div class="flex-shrink-0">固定区域</div>
  </div>
  
  <!-- Grid 布局 -->
  <div class="grid grid-cols-3 gap-4">
    <div class="col-span-2">主要内容</div>
    <div>侧边栏</div>
  </div>
  
  <!-- 定位 -->
  <div class="relative">
    <div class="absolute top-4 right-4">悬浮元素</div>
  </div>
</template>
```

### 2. 尺寸和间距

```vue
<template>
  <!-- 固定尺寸：px 单位 -->
  <div class="w-300px h-200px">固定大小</div>
  
  <!-- 响应式尺寸：rem 单位 -->
  <div class="w-20 h-12 p-4 m-2">响应式</div>
  
  <!-- 百分比尺寸 -->
  <div class="w-full h-screen">全屏</div>
  <div class="w-1/2 w-1/3 w-1/4">分栏</div>
  
  <!-- 间距系统 -->
  <div class="p-4 px-6 py-2">内边距</div>
  <div class="m-4 mx-auto my-8">外边距</div>
  <div class="space-x-4 space-y-2">子元素间距</div>
</template>
```

### 3. 颜色系统

```vue
<template>
  <!-- 文本颜色 -->
  <p class="text-gray-900">主要文本</p>
  <p class="text-gray-600">次要文本</p>
  <p class="text-gray-400">辅助文本</p>
  
  <!-- 背景颜色 -->
  <div class="bg-white">白色背景</div>
  <div class="bg-blue-500">主题色背景</div>
  <div class="bg-gray-50">浅灰背景</div>
  
  <!-- 十六进制颜色（项目特有） -->
  <div class="bg-#f5f5f5 text-#333">自定义颜色</div>
  
  <!-- 渐变色 -->
  <div class="bg-gradient-to-r from-blue-500 to-purple-600">渐变</div>
</template>
```

### 4. 字体和文本

```vue
<template>
  <!-- 字体大小 -->
  <h1 class="text-4xl font-bold">大标题</h1>
  <h2 class="text-2xl font-semibold">中标题</h2>
  <p class="text-base">正文</p>
  <small class="text-sm text-gray-500">小字</small>
  
  <!-- 文本对齐 -->
  <p class="text-left">左对齐</p>
  <p class="text-center">居中</p>
  <p class="text-right">右对齐</p>
  
  <!-- 文本装饰 -->
  <a class="underline hover:no-underline">链接</a>
  <p class="line-through">删除线</p>
  
  <!-- 文本省略 -->
  <p class="truncate">单行省略</p>
  <p class="line-clamp-2">多行省略</p>
</template>
```

## 响应式设计

### 1. 断点系统

```vue
<template>
  <!-- 移动优先的响应式设计 -->
  <div class="w-full md:w-1/2 lg:w-1/3 xl:w-1/4">
    响应式宽度
  </div>
  
  <!-- 不同屏幕下的显示/隐藏 -->
  <div class="block md:hidden">移动端显示</div>
  <div class="hidden md:block">桌面端显示</div>
  
  <!-- 响应式间距 -->
  <div class="p-4 md:p-6 lg:p-8">响应式内边距</div>
  
  <!-- 响应式布局 -->
  <div class="flex-col md:flex-row">
    <div class="w-full md:w-2/3">主内容</div>
    <div class="w-full md:w-1/3">侧边栏</div>
  </div>
</template>
```

### 2. 容器系统

```vue
<template>
  <!-- 标准容器 -->
  <div class="container mx-auto px-4">
    <div class="max-w-7xl mx-auto">最大宽度限制</div>
  </div>
  
  <!-- 项目自定义容器（如果配置了） -->
  <div class="container-lg">大容器</div>
  <div class="container-sm">小容器</div>
</template>
```

## Sass 高级样式

### 1. 嵌套和选择器

```scss
<style scoped lang="scss">
.component {
  background: white;
  border-radius: 8px;
  
  // 嵌套选择器
  .header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    
    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .content {
    padding: 16px;
  }
  
  // 伪类选择器
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  // 状态选择器
  &.active {
    border-color: var(--el-color-primary);
  }
  
  &.disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}
</style>
```

### 2. 变量和混入

```scss
<style scoped lang="scss">
// 局部变量
$component-padding: 16px;
$transition-base: all 0.3s ease;

.component {
  padding: $component-padding;
  transition: $transition-base;
  
  // 使用 CSS 变量（推荐）
  color: var(--el-text-color-primary);
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
}

// 混入示例
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: $transition-base;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.custom-button {
  @include button-base;
  background: var(--el-color-primary);
  color: white;
  
  &:hover {
    background: var(--el-color-primary-light-3);
  }
}
</style>
```

### 3. 响应式混入

```scss
<style scoped lang="scss">
// 响应式断点混入
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}

.responsive-component {
  display: flex;
  
  @include mobile {
    flex-direction: column;
    gap: 8px;
  }
  
  @include tablet {
    flex-direction: row;
    gap: 16px;
  }
  
  @include desktop {
    gap: 24px;
  }
}
</style>
```

## 主题系统

### 1. CSS 变量主题

```scss
// 在 cityStore 中动态更新主题
const updateCityTheme = (cityCode: CityCode) => {
  const themes = {
    gx: {
      '--color-primary': '#1677ff',
      '--color-secondary': '#00b96b',
      '--bg-pattern': 'url(/images/gx-bg.jpg)'
    },
    gl: {
      '--color-primary': '#52c41a',
      '--color-secondary': '#13c2c2',
      '--bg-pattern': 'url(/images/gl-bg.jpg)'
    }
  }
  
  const theme = themes[cityCode]
  Object.entries(theme).forEach(([key, value]) => {
    document.documentElement.style.setProperty(key, value)
  })
}
```

```scss
<style scoped lang="scss">
.themed-component {
  // 使用主题变量
  background: var(--color-primary);
  color: white;
  
  &::before {
    content: '';
    background-image: var(--bg-pattern);
    background-size: cover;
  }
  
  .secondary-element {
    border-color: var(--color-secondary);
  }
}
</style>
```

### 2. Element Plus 主题定制

```scss
<style scoped lang="scss">
// 使用 Element Plus CSS 变量
.custom-form {
  .el-input {
    --el-input-border-color: var(--el-color-primary);
    --el-input-focus-border-color: var(--el-color-primary);
  }
  
  .el-button {
    --el-button-text-color: var(--el-color-primary);
    --el-button-border-color: var(--el-color-primary);
  }
}

// 深度选择器修改子组件样式
.form-container {
  :deep(.el-form-item__label) {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
  
  :deep(.el-input__wrapper) {
    border-radius: 8px;
  }
}
</style>
```

## 动画和过渡

### 1. CSS 过渡

```vue
<template>
  <div class="animated-card">
    <Transition name="fade" mode="out-in">
      <div v-if="visible" class="content">
        内容区域
      </div>
    </Transition>
  </div>
</template>

<style scoped lang="scss">
.animated-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

// Vue 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
```

### 2. UnoCSS 动画

```vue
<template>
  <!-- 使用 UnoCSS 动画类 -->
  <div class="animate-bounce">弹跳动画</div>
  <div class="animate-pulse">脉冲动画</div>
  <div class="animate-spin">旋转动画</div>
  
  <!-- 过渡效果 -->
  <button class="transition-all duration-300 hover:scale-105">
    悬停放大
  </button>
  
  <!-- 自定义动画 -->
  <div class="animate-fade-in">淡入动画</div>
</template>
```

## 性能优化

### 1. 样式性能

```scss
<style scoped lang="scss">
// 避免深层嵌套（超过3层）
.component {
  .level1 {
    .level2 {
      .level3 {
        // 最多3层嵌套
      }
    }
  }
}

// 使用高效选择器
.specific-class {
  // 推荐：类选择器
}

#unique-id {
  // 适用：ID选择器
}

div.class-name {
  // 避免：标签+类选择器
}

* {
  // 避免：通配符选择器
}
```

### 2. 关键CSS和懒加载

```vue
<script setup lang="ts">
// 条件加载样式
const loadHeavyStyles = () => {
  import('./heavy-styles.scss')
}

// 动态主题加载
const loadTheme = async (theme: string) => {
  const { default: styles } = await import(`./themes/${theme}.scss`)
  return styles
}
</script>
```

## 调试和工具

### 1. 样式调试

```vue
<template>
  <!-- 开发环境边框调试 -->
  <div class="debug-borders">
    <div class="border border-red-500 border-dashed">
      调试边框
    </div>
  </div>
</template>

<style scoped lang="scss">
// 开发环境样式调试
@if $env == 'development' {
  .debug-borders * {
    outline: 1px solid red;
  }
}
</style>
```

### 2. 浏览器兼容性

```scss
<style scoped lang="scss">
.modern-component {
  // 现代浏览器特性
  display: grid;
  gap: 16px;
  
  // 回退方案
  @supports not (display: grid) {
    display: flex;
    flex-wrap: wrap;
    
    > * {
      margin: 8px;
    }
  }
  
  // Flexbox 回退
  @supports not (gap: 16px) {
    > * + * {
      margin-left: 16px;
    }
  }
}
</style>
```