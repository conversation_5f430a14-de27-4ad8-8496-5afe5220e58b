---
description: Multi-city portal architecture and city management system
globs: src/stores/cityStore.ts,src/router/index.ts,src/views/homepages/**/*.vue
---

# 多城市门户架构系统

## 城市系统概述

这是一个支持14个广西城市的门户网站，每个城市有独立的路由、主题和页面配置。

### 支持的城市列表

| 城市代码 | 城市名称 | 路径示例 | 状态 |
|---------|---------|----------|------|
| `gx` | 广西 | `/` | 默认城市 |
| `gl` | 桂林 | `/gl` | 激活 |
| `lz` | 柳州 | `/lz` | 激活 |
| `wz` | 梧州 | `/wz` | 激活 |
| `bs` | 百色 | `/bs` | 激活 |
| `qz` | 钦州 | `/qz` | 激活 |
| `hc` | 河池 | `/hc` | 激活 |
| `bh` | 北海 | `/bh` | 激活 |
| `fg` | 防港 | `/fg` | 激活 |
| `yl` | 玉林 | `/yl` | 激活 |
| `cz` | 崇左 | `/cz` | 激活 |
| `gg` | 贵港 | `/gg` | 激活 |
| `lb` | 来宾 | `/lb` | 激活 |
| `hz` | 贺州 | `/hz` | 激活 |
| `nn` | 南宁 | `/nn` | 激活 |

## 核心架构文件

### 1. 城市状态管理 ([cityStore.ts](mdc:src/stores/cityStore.ts))

```typescript
// 城市 Store 是整个多城市系统的核心
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()

// 基础操作
cityStore.setCurrentCity('gl')  // 切换到桂林
cityStore.cityInfo             // 当前城市信息
cityStore.currentCityCode      // 当前城市代码

// 路径生成
cityStore.getCityHomePath      // 当前城市首页路径
cityStore.getCityPagePath('about')  // 当前城市页面路径

// 城市验证
cityStore.isSupportedCity('gl')     // 检查城市是否支持
cityStore.initializeFromRoute('gl') // 从路由初始化城市状态
```

### 2. 动态路由系统 ([router/index.ts](mdc:src/router/index.ts))

路由模式：`/:city(gl|lz|wz|bs|qz|hc|bh|fg|yl|cz|gg|lb|hz|nn)?`

```typescript
// 路由配置核心逻辑
{
  path: '/:city(gl|lz|wz|bs|qz|hc|bh|fg|yl|cz|gg|lb|hz|nn)?',
  component: AppLayout,
  beforeEnter: (to, _from, next) => {
    const cityStore = useCityStore()
    const cityCode = (to.params.city as string) || 'gx'
    
    // 城市验证和状态初始化
    if (cityCode !== 'gx' && !cityStore.isSupportedCity(cityCode)) {
      next('/')  // 不支持的城市重定向到广西
      return
    }
    
    cityStore.initializeFromRoute(cityCode)
    next()
  }
}
```

### 3. 动态组件加载

```typescript
// 动态导入城市首页组件
const getCityHomePage = (cityCode: string) => {
  return () => import(`@/views/homepages/${cityCode}/HomePage.vue`).catch(() => {
    console.warn(`城市 ${cityCode} 的首页组件不存在，使用默认首页`)
    return import('@/views/homepages/gx/HomePage.vue')
  })
}
```

## 添加新城市的完整流程

### 1. 更新城市配置

在 [cityStore.ts](mdc:src/stores/cityStore.ts) 的 `CITIES_CONFIG` 中添加新城市：

```typescript
const CITIES_CONFIG: Record<CityCode, CityInfo> = {
  // 现有城市...
  
  // 新城市配置
  newcity: {
    code: 'newcity',
    name: '新城市',
    nameEn: 'NewCity',
    description: '新城市门户网站',
    isActive: true,
    id: 15
  }
}
```

### 2. 更新路由模式

在 [router/index.ts](mdc:src/router/index.ts) 中添加新城市代码：

```typescript
// 更新路由匹配模式
path: '/:city(gl|lz|wz|bs|qz|hc|bh|fg|yl|cz|gg|lb|hz|nn|newcity)?'
```

### 3. 创建城市首页

创建新文件：`src/views/homepages/newcity/HomePage.vue`

```vue
<template>
  <div class="newcity-homepage">
    <h1>{{ cityStore.cityInfo.name }}门户网站</h1>
    <!-- 城市特色内容 -->
  </div>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()
</script>
```

### 4. 更新类型定义

在 [src/types/index.ts](mdc:src/types/index.ts) 中添加新城市代码：

```typescript
export type CityCode = 'gx' | 'gl' | 'lz' | 'wz' | 'bs' | 'qz' | 'hc' | 'bh' | 'fg' | 'yl' | 'cz' | 'gg' | 'lb' | 'hz' | 'nn' | 'newcity'
```

## 城市切换和导航

### 1. 编程式导航

```typescript
import { useRouter } from 'vue-router'
import { useCityStore } from '@/stores/cityStore'

const router = useRouter()
const cityStore = useCityStore()

// 切换到指定城市首页
const goToCity = (cityCode: string) => {
  if (cityStore.isSupportedCity(cityCode)) {
    cityStore.setCurrentCity(cityCode)
    router.push(cityStore.getCityHomePath)
  }
}

// 导航到当前城市的特定页面
const goToPage = (pageName: string) => {
  router.push(cityStore.getCityPagePath(pageName))
}
```

### 2. 模板中的路径生成

```vue
<template>
  <!-- 使用 cityStore 生成正确的路径 -->
  <router-link :to="cityStore.getCityHomePath">
    回到{{ cityStore.cityInfo.name }}首页
  </router-link>
  
  <router-link :to="cityStore.getCityPagePath('about')">
    关于{{ cityStore.cityInfo.name }}
  </router-link>
</template>
```



## 开发注意事项

### 1. 城市代码验证
- 所有城市相关操作都必须通过 `cityStore.isSupportedCity()` 验证
- 不支持的城市自动重定向到广西首页

### 2. 组件容错
- 城市首页组件缺失时自动降级到默认首页
- 路由守卫确保城市状态正确初始化

### 3. 状态持久化
- 用户选择的城市通过 `pinia-plugin-persistedstate` 持久化
- 页面刷新后自动恢复城市状态

### 4. SEO 友好
- 每个城市使用独立的路径，便于搜索引擎索引
- 城市信息可用于生成动态 meta 标签