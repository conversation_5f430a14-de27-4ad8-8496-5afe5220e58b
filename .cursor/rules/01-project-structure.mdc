---
alwaysApply: true
description: Core project structure and development guidelines for the multi-city portal website
---

# 广西就业门户网站 - 项目结构与开发规范

This is a multi-city portal website for Guangxi Province employment services, built with Vue 3 + TypeScript + Vite.

## 项目架构核心

### 技术栈
- **Vue 3** + Composition API + TypeScript
- **Element Plus** UI 组件库 (自动导入)
- **UnoCSS** 原子化 CSS
- **Pinia** 状态管理
- **Vue Router 4** 动态路由
- **Vite** 构建工具

### 开发工具链
- **包管理器**: pnpm (必须使用)
- **开发命令**: `pnpm dev` (启动服务器)
- **构建命令**: `pnpm build` (包含类型检查)
- **类型检查**: `pnpm type-check`
- **代码检查**: `pnpm lint` / `pnpm lint:fix`

## 核心文件结构

### 关键配置文件
- [vite.config.ts](mdc:vite.config.ts) - Vite 配置，包含路径别名、代理设置、自动导入
- [package.json](mdc:package.json) - 项目依赖和脚本
- [tsconfig.json](mdc:tsconfig.json) - TypeScript 配置
- [uno.config.ts](mdc:uno.config.ts) - UnoCSS 配置

### 核心目录
- `src/stores/` - Pinia 状态管理，[cityStore.ts](mdc:src/stores/cityStore.ts) 为核心
- `src/router/` - Vue Router 配置，[index.ts](mdc:src/router/index.ts) 包含动态城市路由
- `src/api-services/` - 自动生成的 TypeScript API 客户端
- `src/views/homepages/{cityCode}/` - 各城市首页组件
- `src/components/` - 公共组件库
- `src/utils/` - 工具函数，[axios-utils.ts](mdc:src/utils/axios-utils.ts) 为 API 配置

## 路径别名使用规范

在代码中必须使用以下路径别名：

```typescript
// 推荐使用
import { useCityStore } from '@/stores/cityStore'
import HeaderComponent from '@/components/layout/HeaderComponent.vue'
import { PositionApi } from '@/api-services'
import type { CityCode } from '@/types'

// 避免相对路径
import HeaderComponent from '../../../components/layout/HeaderComponent.vue'
```

## 自动导入配置

以下 API 无需手动引入：
- Vue Composition API: `ref`, `reactive`, `computed`, `watch`, `onMounted` 等
- Vue Router: `useRouter`, `useRoute`
- Pinia: `defineStore`, `storeToRefs`
- Element Plus 组件: `ElButton`, `ElForm` 等
- VueUse: `useLocalStorage`, `useSessionStorage`, `useDark`, `useToggle`

## 代码质量标准

### 开发前检查
1. 确保 Node.js >= 16 和 pnpm >= 8.0
2. 运行 `pnpm install` 安装依赖
3. 运行 `pnpm type-check` 确保类型正确

### 提交前检查
1. `pnpm type-check` - TypeScript 类型检查
2. `pnpm lint` - ESLint 代码检查  
3. `pnpm build` - 构建测试
4. `pnpm test` - 功能测试

### 命名规范
- **Vue 组件**: PascalCase (如 `HeaderComponent.vue`)
- **Store 文件**: camelCase (如 `cityStore.ts`)  
- **工具函数**: kebab-case (如 `axios-utils.ts`)
- **类型定义**: PascalCase (如 `CityCode`, `CityInfo`)

## API 代理配置

开发环境 API 代理到: `http://************:5020`
- 本地访问: `http://localhost:3000`
- API 路径: `/api/*` 自动代理

## 构建和部署

```bash
# 开发环境
pnpm dev

# 生产构建
pnpm build

# 预览构建结果  
pnpm preview
```

构建产物在 `dist/` 目录，包含资源文件分类和哈希命名。