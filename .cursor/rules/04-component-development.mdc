---
description: Component development patterns, standards and best practices
globs: src/components/**/*.vue,src/views/**/*.vue,src/layout/**/*.vue
---

# Vue 组件开发规范

## 组件开发标准

### 1. 组件分类和命名

#### 组件分类
- **布局组件** (`src/components/layout/`): 页面结构组件
- **公共组件** (`src/components/common/`): 通用功能组件  
- **页面组件** (`src/views/`): 路由对应的页面组件
- **业务组件** (`src/views/{page}/component/`): 页面私有组件

#### 命名规范
```bash
# 组件文件：PascalCase
HeaderComponent.vue
CitySelector.vue
SearchWrapper.vue

# 业务组件：camelCase
industry.vue
banner.vue
```

### 2. 标准组件结构

```vue
<template>
  <!-- 模板：使用 UnoCSS 原子化类名 -->
  <div class="component-name">
    <div class="flex items-center justify-between p-4">
      <!-- 组件内容 -->
    </div>
  </div>
</template>

<script setup lang="ts">
// 1. 导入依赖 - 自动导入的无需手动导入
import { useCityStore } from '@/stores/cityStore'
import type { CityCode } from '@/types'

// 2. Props 定义
interface Props {
  title?: string
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  disabled: false
})

// 3. Emits 定义
interface Emits {
  change: [value: string]
  click: [event: MouseEvent]
}

const emit = defineEmits<Emits>()

// 4. Store 使用
const cityStore = useCityStore()

// 5. 响应式数据
const loading = ref(false)
const data = ref([])

// 6. 计算属性
const computedClass = computed(() => {
  return {
    'is-disabled': props.disabled,
    [`size-${props.size}`]: true
  }
})

// 7. 方法定义
const handleClick = (event: MouseEvent) => {
  if (props.disabled) return
  emit('click', event)
}

// 8. 生命周期
onMounted(() => {
  // 组件挂载后的逻辑
})
</script>

<style scoped lang="scss">
// 组件私有样式
.component-name {
  // 使用 CSS 变量适配主题
  background-color: var(--el-bg-color);
  border-color: var(--el-border-color);
  
  // 响应式设计
  @media (max-width: 768px) {
    flex-direction: column;
  }
}
</style>
```

## 核心组件模式

### 1. 布局组件模式

参考 [HeaderComponent.vue](mdc:src/components/layout/HeaderComponent.vue):

```vue
<template>
  <header class="header">
    <!-- 使用城市路径生成 -->
    <router-link 
      v-for="menu in navigationMenus"
      :key="menu.path"
      :to="cityStore.getCityPagePath(menu.path)"
      class="nav-item"
    >
      {{ menu.name }}
    </router-link>
  </header>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()

// 导航菜单配置
const navigationMenus = computed(() => [
  { name: "首页", path: "" },
  { name: "招聘专题", path: "recruitment" },
  // ...
])

// 路由状态判断
const isMenuActive = (path: string) => {
  const currentPath = router.currentRoute.value.path
  return cityStore.isCurrentPath(path)(currentPath)
}
</script>
```

### 2. 功能组件模式

参考 [CitySelector.vue](mdc:src/components/common/CitySelector.vue):

```vue
<template>
  <div class="city-selector">
    <el-dropdown @command="handleCityChange">
      <div class="flex items-center space-x-2">
        <span>{{ cityStore.cityInfo.name }}</span>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            v-for="city in cityStore.availableCities"
            :key="city.code"
            :command="city.code"
          >
            {{ city.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'
import { goToCity } from '@/router'
import type { CityCode } from '@/types'

const cityStore = useCityStore()

const handleCityChange = (cityCode: CityCode) => {
  const success = goToCity(cityCode)
  if (success) {
    ElMessage.success(`已切换到${cityStore.getCityByCode(cityCode)?.name}`)
  }
}
</script>
```

### 3. 页面组件模式

参考 [HomePage.vue](mdc:src/views/homepages/gx/HomePage.vue):

```vue
<template>
  <div class="gx-homepage">
    <div class="container-lg mt-20px">
      <div class="flex justify-between h-340px">
        <!-- 使用 UnoCSS 原子化类名 -->
        <div class="w-256px bg-#fff rounded-8px">
          <Industry />
        </div>
        <div class="w-605px bg-#fff">
          中间区域
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入页面私有组件
import Industry from './component/industry.vue'
</script>
```

## 样式开发规范

### 1. UnoCSS 原子化优先

```vue
<template>
  <!-- 推荐：使用 UnoCSS 原子化类名 -->
  <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
    <h1 class="text-2xl font-bold text-gray-900">标题</h1>
    <button class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
      按钮
    </button>
  </div>
</template>
```

### 2. Sass 补充样式

```scss
<style scoped lang="scss">
// 复杂样式使用 Sass
.complex-component {
  // 使用 CSS 变量适配主题
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  
  // 嵌套选择器
  .inner-element {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
  
  // 响应式断点
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}
</style>
```

### 3. 主题变量使用

```scss
// 使用 Element Plus 主题变量
.themed-component {
  color: var(--el-text-color-primary);
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
  
  // 状态颜色
  &.success {
    color: var(--el-color-success);
  }
  
  &.warning {
    color: var(--el-color-warning);
  }
}
```

## 组件通信模式

### 1. Props 向下传递

```vue
<!-- 父组件 -->
<template>
  <ChildComponent 
    :title="pageTitle"
    :config="componentConfig"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
interface ComponentConfig {
  size: 'small' | 'large'
  theme: string
}

const pageTitle = ref('页面标题')
const componentConfig: ComponentConfig = {
  size: 'large',
  theme: 'primary'
}

const handleChange = (value: string) => {
  console.log('子组件值变化:', value)
}
</script>
```

### 2. Events 向上传递

```vue
<!-- 子组件 -->
<script setup lang="ts">
interface Props {
  title: string
  config: ComponentConfig
}

interface Emits {
  change: [value: string]
  update: [data: any]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleInput = (value: string) => {
  emit('change', value)
}
</script>
```

### 3. 跨组件通信 - Pinia

```vue
<script setup lang="ts">
// 任何组件都可以访问全局状态
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()

// 读取状态
const currentCity = computed(() => cityStore.cityInfo)

// 修改状态
const switchCity = (cityCode: string) => {
  cityStore.setCurrentCity(cityCode)
}
</script>
```

## 异步数据处理

### 1. 基础异步模式

```vue
<script setup lang="ts">
import { PositionApi } from '@/api-services'
import { getPcAPI } from '@/utils/axios-utils'

const positionApi = getPcAPI(PositionApi)

// 异步状态管理
const loading = ref(false)
const data = ref([])
const error = ref<string | null>(null)

const fetchData = async () => {
  loading.value = true
  error.value = null
  
  try {
    const response = await positionApi.apiPositionSearchOptionGet()
    data.value = response.data
  } catch (err) {
    error.value = '数据加载失败'
    console.error(err)
  } finally {
    loading.value = false
  }
}

onMounted(fetchData)
</script>
```

### 2. 组合式函数模式

```typescript
// src/composables/usePositionData.ts
export const usePositionData = () => {
  const positions = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  const fetchPositions = async (params: SearchParams) => {
    loading.value = true
    try {
      const response = await positionApi.apiPositionSearchPost({ params })
      positions.value = response.data
    } catch (err) {
      error.value = err
    } finally {
      loading.value = false
    }
  }
  
  return {
    positions: readonly(positions),
    loading: readonly(loading),
    error: readonly(error),
    fetchPositions
  }
}
```

```vue
<!-- 在组件中使用 -->
<script setup lang="ts">
import { usePositionData } from '@/composables/usePositionData'

const { positions, loading, error, fetchPositions } = usePositionData()

onMounted(() => {
  fetchPositions({ keyword: '前端开发' })
})
</script>
```

## 性能优化

### 1. 懒加载组件

```vue
<script setup lang="ts">
// 异步组件加载
const HeavyComponent = defineAsyncComponent(() => 
  import('./HeavyComponent.vue')
)

// 条件渲染优化
const showHeavyComponent = ref(false)
</script>

<template>
  <div>
    <button @click="showHeavyComponent = true">
      加载重型组件
    </button>
    
    <Suspense v-if="showHeavyComponent">
      <HeavyComponent />
      <template #fallback>
        <div>加载中...</div>
      </template>
    </Suspense>
  </div>
</template>
```

### 2. 虚拟列表

```vue
<template>
  <!-- 大量数据渲染优化 -->
  <div class="virtual-list" :style="{ height: '400px' }">
    <div 
      v-for="item in visibleItems" 
      :key="item.id"
      class="list-item"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script setup lang="ts">
const allItems = ref([]) // 全部数据
const visibleItems = computed(() => {
  // 虚拟滚动逻辑，只渲染可见项
  return allItems.value.slice(startIndex.value, endIndex.value)
})
</script>
```

### 3. 缓存和防抖

```vue
<script setup lang="ts">
import { debounce } from 'lodash-es'

// 搜索防抖
const searchKeyword = ref('')
const searchResults = ref([])

const debouncedSearch = debounce(async (keyword: string) => {
  if (!keyword) return
  
  const response = await positionApi.apiPositionSearchPost({
    keyword
  })
  searchResults.value = response.data
}, 300)

watch(searchKeyword, debouncedSearch)

// 计算属性缓存
const expensiveComputed = computed(() => {
  return heavyCalculation(someReactiveData.value)
})
</script>
```

## 组件测试

### 1. 组件单元测试

```typescript
// tests/components/CitySelector.test.ts
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import CitySelector from '@/components/common/CitySelector.vue'

describe('CitySelector', () => {
  it('should render city name correctly', () => {
    const pinia = createPinia()
    const wrapper = mount(CitySelector, {
      global: {
        plugins: [pinia]
      }
    })
    
    expect(wrapper.text()).toContain('广西')
  })
  
  it('should emit city change event', async () => {
    // 测试事件触发
    await wrapper.find('.city-option').trigger('click')
    expect(wrapper.emitted('change')).toBeTruthy()
  })
})
```

### 2. 集成测试

```typescript
// tests/views/HomePage.test.ts
describe('HomePage Integration', () => {
  it('should display correct city data', async () => {
    const wrapper = mount(HomePage, {
      global: {
        plugins: [pinia, router]
      }
    })
    
    await flushPromises() // 等待异步操作完成
    expect(wrapper.find('.city-name').text()).toBe('广西')
  })
})
```