---
alwaysApply: true
description: Project overview and quick reference guide
---

# 广西就业门户网站 - 项目概览

## 🏗️ 项目简介

这是一个基于 Vue 3 + TypeScript + Vite 构建的多城市门户网站，支持广西全区14个城市的个性化门户展示。

### 核心特性

- **多城市架构**: 支持14个广西城市独立配置
- **动态路由**: `/:city/page` 格式的灵活路由系统
- **类型安全**: 完整的 TypeScript 支持和自动生成的 API 客户端
- **现代开发**: Vue 3 Composition API + Pinia + Element Plus + UnoCSS

## 📁 核心文件快速定位

### 🔧 配置文件
- [package.json](mdc:package.json) - 项目依赖和命令
- [vite.config.ts](mdc:vite.config.ts) - 构建配置和路径别名
- [tsconfig.json](mdc:tsconfig.json) - TypeScript 配置
- [uno.config.ts](mdc:uno.config.ts) - UnoCSS 原子化样式配置

### 🏛️ 架构核心
- [src/stores/cityStore.ts](mdc:src/stores/cityStore.ts) - 多城市状态管理核心
- [src/router/index.ts](mdc:src/router/index.ts) - 动态路由配置
- [src/utils/axios-utils.ts](mdc:src/utils/axios-utils.ts) - API 配置和拦截器
- [src/types/index.ts](mdc:src/types/index.ts) - 核心类型定义

### 🧩 组件系统
- `src/components/layout/` - 布局组件 (Header, Footer 等)
- `src/components/common/` - 公共组件 (CitySelector, SearchWrapper 等)
- `src/views/homepages/{cityCode}/` - 各城市首页组件

### 🌐 API 服务
- `src/api-services/` - Swagger 自动生成的 TypeScript API 客户端
- `src/api-services/apis/` - 各业务模块 API (Position, User, Enterprise 等)

## 🚀 快速开始

### 开发命令

```bash
pnpm install          # 安装依赖
pnpm dev              # 启动开发服务器
pnpm build            # 构建生产版本
pnpm type-check       # TypeScript 类型检查
pnpm lint             # 代码检查
```

### 开发流程

1. **修改前**: `pnpm type-check` 确保类型正确
2. **提交前**: `pnpm lint` 和 `pnpm build` 确保代码质量
3. **测试**: `pnpm test` 验证功能

## 🏙️ 多城市系统

### 支持的城市

| 代码 | 城市 | 路径 | 状态 |
|------|------|------|------|
| `gx` | 广西 | `/` | 默认 ✅ |
| `gl` | 桂林 | `/gl` | 激活 ✅ |
| `lz` | 柳州 | `/lz` | 激活 ✅ |
| 其他11个城市... | | | 激活 ✅ |

### 核心操作

```typescript
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()

// 切换城市
cityStore.setCurrentCity('gl')

// 生成路径
cityStore.getCityHomePath
cityStore.getCityPagePath('about')

// 验证城市
cityStore.isSupportedCity('gl')
```

## 🔌 API 集成模式

### 标准使用

```typescript
import { PositionApi } from '@/api-services'
import { getPcAPI } from '@/utils/axios-utils'

const positionApi = getPcAPI(PositionApi)

// 发起请求
const response = await positionApi.apiPositionSearchOptionGet()
```

### 自动功能

- ✅ Token 自动管理和刷新
- ✅ 401 错误自动跳转登录
- ✅ 统一错误提示
- ✅ 请求/响应拦截器
- ✅ 完整的 TypeScript 类型安全

## 🎨 样式开发

### 优先级顺序

1. **UnoCSS 原子化** - `class="flex items-center p-4"`
2. **Element Plus** - UI 组件默认样式
3. **Sass 自定义** - 复杂样式和主题
4. **CSS 变量** - 主题切换

### 响应式设计

```vue
<template>
  <!-- 移动优先 -->
  <div class="w-full md:w-1/2 lg:w-1/3">
    响应式容器
  </div>
  
  <!-- 条件显示 -->
  <div class="block md:hidden">移动端</div>
  <div class="hidden md:block">桌面端</div>
</template>
```

## 📝 组件开发标准

### 基础结构

```vue
<template>
  <div class="component-name">
    <!-- 使用 UnoCSS 原子化类名 -->
  </div>
</template>

<script setup lang="ts">
// 1. 导入 (自动导入无需手动)
import { useCityStore } from '@/stores/cityStore'

// 2. Props 和 Emits
interface Props {
  title?: string
}

interface Emits {
  change: [value: string]
}

// 3. 组件逻辑
</script>

<style scoped lang="scss">
// 4. 组件样式
</style>
```

### 命名规范

- **组件文件**: PascalCase (`HeaderComponent.vue`)
- **Store 文件**: camelCase (`cityStore.ts`)
- **工具函数**: kebab-case (`axios-utils.ts`)

## 🔍 问题排查

### 常见问题

1. **路由跳转异常**: 检查城市代码是否在 `CITIES_CONFIG` 中存在
2. **API 请求失败**: 确认 API 代理配置和环境变量
3. **样式不生效**: 检查 UnoCSS 类名拼写和 Sass 编译
4. **类型错误**: 运行 `pnpm type-check` 查看详细错误

### 开发工具

- **IDE**: VSCode + Vetur + TypeScript Hero
- **调试**: Chrome DevTools + Vue DevTools
- **代码质量**: Prettier + ESLint + Husky

## 📚 详细规则文档

- [01-project-structure.mdc](.cursor/rules/01-project-structure.mdc) - 项目结构和开发规范
- [02-multi-city-architecture.mdc](.cursor/rules/02-multi-city-architecture.mdc) - 多城市架构系统
- [03-api-integration.mdc](.cursor/rules/03-api-integration.mdc) - API 集成与接口管理
- [04-component-development.mdc](.cursor/rules/04-component-development.mdc) - Vue 组件开发规范
- [05-styling-guidelines.mdc](.cursor/rules/05-styling-guidelines.mdc) - 样式开发指南

---

💡 **提示**: 这些规则将帮助 Claude 更好地理解项目结构和开发标准，提供更准确的开发建议。