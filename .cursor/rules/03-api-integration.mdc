---
description: API integration patterns and Swagger-generated TypeScript client usage
globs: src/api-services/**/*.ts,src/utils/axios-utils.ts
---

# API 集成与接口管理

## API 架构概述

项目使用 Swagger 自动生成的 TypeScript API 客户端，提供完整的类型安全和接口文档。

### 核心配置文件
- [src/utils/axios-utils.ts](mdc:src/utils/axios-utils.ts) - Axios 配置、拦截器、Token 管理
- [src/api-services/index.ts](mdc:src/api-services/index.ts) - API 导出入口
- [src/api-services/base.ts](mdc:src/api-services/base.ts) - 基础 API 配置

## API 客户端使用模式

### 1. 基础配置

```typescript
// 环境配置
export const serveConfig = new Configuration({
    basePath: import.meta.env.VITE_API_URL  // 来自 .env 文件
});

// 获取 API 实例的标准方式
import { PositionApi } from '@/api-services';
import { getPcAPI } from '@/utils/axios-utils';

const positionApi = getPcAPI(PositionApi);
```

### 2. 主要 API 服务

| API 服务 | 文件 | 主要功能 |
|---------|------|---------|
| `PositionApi` | [position-api.ts](mdc:src/api-services/apis/position-api.ts) | 职位搜索、推荐、详情 |
| `UserApi` | [user-api.ts](mdc:src/api-services/apis/user-api.ts) | 用户认证、个人信息 |
| `EnterpriseApi` | [enterprise-api.ts](mdc:src/api-services/apis/enterprise-api.ts) | 企业信息、展示 |
| `NewsApi` | [news-api.ts](mdc:src/api-services/apis/news-api.ts) | 新闻资讯、公告 |
| `MeetingApi` | [meeting-api.ts](mdc:src/api-services/apis/meeting-api.ts) | 招聘会、活动 |
| `DataApi` | [data-api.ts](mdc:src/api-services/apis/data-api.ts) | 基础数据、字典 |
| `LogApi` | [log-api.ts](mdc:src/api-services/apis/log-api.ts) | 行为日志、统计 |

## 标准使用模式

### 1. 基础请求模式

```typescript
import { PositionApi, BussDistrict, ApplicationPlatform } from '@/api-services';
import { getPcAPI } from '@/utils/axios-utils';

// 获取 API 实例
const positionApi = getPcAPI(PositionApi);

// 发起请求 - 自动类型提示和验证
try {
  const response = await positionApi.apiPositionSearchOptionGet({
    districtId: BussDistrict.Guilin,
    from: ApplicationPlatform.Web
  });
  
  console.log('搜索配置:', response.data);
} catch (error) {
  // 错误已由拦截器自动处理
  console.error('请求失败:', error);
}
```

### 2. 分页查询模式

```typescript
// 分页职位搜索
const searchPositions = async (params: {
  keyword?: string;
  pageIndex: number;
  pageSize: number;
}) => {
  const response = await positionApi.apiPositionSearchPost({
    positionRequest: {
      keyword: params.keyword,
      pageIndex: params.pageIndex,
      pageSize: params.pageSize,
      districtId: BussDistrict.Guilin
    }
  });
  
  return response.data;
};
```

### 3. 文件上传模式

```typescript
import { UserApi } from '@/api-services';

const userApi = getPcAPI(UserApi);

// 文件上传
const uploadAvatar = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await userApi.apiUserUploadAvatarPost({
    file: formData
  });
  
  return response.data;
};
```

## Token 管理系统

### 1. 自动 Token 处理

```typescript
// Token 存储键定义
export const accessTokenKey = 'access-token';
export const refreshAccessTokenKey = `x-${accessTokenKey}`;

// Token 获取和清理
export const getToken = () => Local.get(accessTokenKey);
export const clearTokens = () => {
  Local.remove(accessTokenKey);
  Local.remove(refreshAccessTokenKey);
  Session.clear();
};
```

### 2. 请求拦截器 - 自动添加 Token

```typescript
// 请求拦截器自动处理
axiosInstance.interceptors.request.use(
  (config) => {
    // 自动添加 Authorization 头
    const token = getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);
```

### 3. 响应拦截器 - Token 刷新

```typescript
// 响应拦截器处理 401 和 Token 刷新
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // 自动跳转登录
      clearAccessTokens();
      // 路由跳转到登录页
    }
    return Promise.reject(error);
  }
);
```

## 类型安全保障

### 1. 自动生成的类型定义

```typescript
// 所有模型类型都在 models/ 目录下自动生成
import { 
  PositionRequest,           // 职位搜索请求
  PositionDisplayDto,        // 职位显示数据
  BussDistrict,             // 业务地市枚举
  ApplicationPlatform,       // 应用平台枚举
  RestfulResultPositionList  // 标准响应格式
} from '@/api-services';
```

### 2. 响应数据结构

```typescript
// 标准响应格式
interface RestfulResult<T> {
  success: boolean;
  message: string;
  data: T;
  code: number;
  timestamp: string;
}

// 使用示例
const response: RestfulResultPositionList = await positionApi.apiPositionSearchPost({...});
if (response.success) {
  const positions = response.data.items; // 自动类型推断
}
```

## 错误处理策略

### 1. 统一错误处理

```typescript
// 响应拦截器已配置统一错误处理
// 包括：
// ✓ 401 错误自动跳转登录
// ✓ 网络错误统一提示
// ✓ 业务错误信息展示
// ✓ 请求/响应日志记录

// 开发者只需关注业务逻辑
try {
  const result = await positionApi.apiPositionSearchPost({...});
  // 处理成功响应
} catch (error) {
  // 错误已被拦截器处理
  // 可选：添加特定业务错误处理
}
```

### 2. 业务错误处理

```typescript
// 针对特定业务场景的错误处理
const handleApiCall = async () => {
  try {
    const response = await positionApi.apiPositionSearchPost({...});
    
    if (!response.success) {
      // 业务级错误
      ElMessage.error(response.message || '操作失败');
      return;
    }
    
    // 成功处理
    return response.data;
  } catch (error) {
    // 网络或系统级错误（已被拦截器处理）
    console.error('API调用失败:', error);
  }
};
```

## 环境配置

### 1. 开发环境

```bash
# .env.development
VITE_API_URL=http://localhost:8080/api
```

### 2. 生产环境

```bash
# .env.production  
VITE_API_URL=https://api.example.com/api
```

### 3. 代理配置

```typescript
// vite.config.ts 中的代理配置
server: {
  proxy: {
    '/api': {
      target: 'http://************:5020',
      changeOrigin: true,
      // 不重写路径，保持 /api 前缀
    }
  }
}
```

## 最佳实践

### 1. API 组合模式

```typescript
// 在组件中组合多个 API 调用
import { PositionApi, EnterpriseApi } from '@/api-services';
import { getPcAPI } from '@/utils/axios-utils';

const positionApi = getPcAPI(PositionApi);
const enterpriseApi = getPcAPI(EnterpriseApi);

// 并行请求优化
const loadPageData = async () => {
  const [positions, enterprises] = await Promise.all([
    positionApi.apiPositionRecommendGet(),
    enterpriseApi.apiEnterpriseHotGet()
  ]);
  
  return { positions: positions.data, enterprises: enterprises.data };
};
```

### 2. 响应式数据模式

```typescript
// 结合 Vue 3 响应式系统
import { ref, onMounted } from 'vue';

const usePositionData = () => {
  const positions = ref([]);
  const loading = ref(false);
  const error = ref(null);
  
  const fetchPositions = async () => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await positionApi.apiPositionRecommendGet();
      positions.value = response.data;
    } catch (err) {
      error.value = err;
    } finally {
      loading.value = false;
    }
  };
  
  onMounted(fetchPositions);
  
  return { positions, loading, error, fetchPositions };
};
```

### 3. 缓存策略

```typescript
// 结合 VueUse 实现接口缓存
import { useAsyncState } from '@vueuse/core';

const { state: searchOptions, isReady, execute } = useAsyncState(
  () => positionApi.apiPositionSearchOptionGet(),
  null,
  { 
    resetOnExecute: false,  // 不重置之前的数据
    delay: 500             // 防抖
  }
);
```