{"name": "gxjy-portal", "private": true, "version": "1.0.0", "description": "广西门户网站前端项目", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext vue,js,ts,jsx,tsx --max-warnings 0", "lint:fix": "eslint . --ext vue,js,ts,jsx,tsx --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "2.3.2", "@vueuse/core": "^13.6.0", "axios": "1.11.0", "element-plus": "2.10.7", "js-cookie": "3.0.5", "pinia": "2.3.1", "pinia-plugin-persistedstate": "3.2.3", "vue": "3.5.18", "vue-router": "4.5.1"}, "devDependencies": {"@iconify-json/ep": "1.2.3", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@unocss/reset": "66.4.2", "@vitejs/plugin-vue": "5.2.4", "@vue/test-utils": "^2.4.0", "code-inspector-plugin": "1.0.5", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.25.0", "sass": "1.90.0", "typescript": "^5.4.0", "unocss": "0.61.9", "unplugin-auto-import": "0.17.8", "unplugin-vue-components": "0.27.5", "vite": "5.4.19", "vitest": "^1.6.0", "vue-tsc": "2.2.12"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}