<template>
  <div class="default-homepage container-lg py-16">
    <div class="text-center">
      <div class="mb-8">
        <i class="i-ep-warning text-6xl text-yellow-500 mb-4"></i>
        <h1 class="text-3xl font-bold text-gray-900 mb-4">
          页面开发中
        </h1>
        <p class="text-gray-600 text-lg">
          {{ cityStore.cityInfo.name }}的专属首页正在开发中，请稍后再试。
        </p>
      </div>
      
      <div class="space-y-4">
        <router-link 
          :to="`/${cityStore.currentCity}/news`"
          class="btn btn-primary mr-4"
        >
          查看新闻资讯
        </router-link>
        <router-link 
          :to="`/${cityStore.currentCity}/services`"
          class="btn btn-secondary"
        >
          政务服务
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'

const cityStore = useCityStore()
</script>