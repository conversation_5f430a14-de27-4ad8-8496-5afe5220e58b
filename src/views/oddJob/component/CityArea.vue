<script setup lang="ts">
import { BusinessDistrictOutput, KeywordItemDto } from "@/api-services";

type KeywordDto = KeywordItemDto & {
  count: string;
};

const props = withDefaults(
  defineProps<{
    city: Array<KeywordItemDto>;
    cityArea: Array<BusinessDistrictOutput>;
    areaBusiness?: string;
  }>(),
  {
    city: () => [],
    cityArea: () => [],
    areaBusiness: () => "",
  }
);

// const { contrlArea, query,contrlAreaClear } = useRouteConfig();
const areaActiveId = ref<number | undefined>();
const areaBusiness = ref<string>("");
const areaRef = ref<any>(null);
const areaShowExpend = ref(false);
const openExpend = ref(false);

const queryParams = computed(() => {
  const resultArr: { cityId: number; area: Array<number> }[] = [];

  if (areaBusiness.value) {
    const arr = areaBusiness.value.split(",");
    arr.map((item) => {
      const cityArr = item.split(":");
      const areaArr = [];
      if (cityArr.length > 1) {
        areaArr.push(...cityArr[1].split("_").map((item) => Number(item)));
      }
      resultArr.push({ cityId: Number(cityArr[0]), area: areaArr });
    });
  }
  if (resultArr.length > 0) {
    areaActiveId.value = resultArr[resultArr.length - 1].cityId;
  } else {
    areaActiveId.value = 0;
  }
  return resultArr;
});

const querySelectName = computed(() => {
  if (cityList.value.length == 0) return [];
  return queryParams.value.map((item) => {
    const name =
      cityList.value.find((it) => it.keywordID == item.cityId)?.keywordName +
        "：" || "";
    let areaArr: { name: string; key: number }[] = [];
    areaArr = item.area.map((it) => {
      const areaName = props.cityArea.find((i) => i.key == it)?.name || "";
      return { name: areaName, key: it };
    });
    if (areaArr.length == 0) {
      areaArr.push({ name: `全${name.replace("：", "")}`, key: 0 });
    }
    return {
      id: item.cityId,
      name,
      area: areaArr,
    };
  });
});

const cityList = computed(() => {
  const list: Array<KeywordDto> = [
    { keywordName: "不限", keywordID: 0, count: "" },
  ];

  const arr = props.city.map((item) => {
    const result = queryParams.value.find((it) => it.cityId === item.keywordID);
    if (result) {
      const count =
        result.area?.length > 0 ? result.area?.length.toString() : "全";
      return { ...item, count };
    }
    return { ...item, count: "" };
  });
  if (arr.length > 0) {
    return list.concat(arr as Array<KeywordDto>);
  } else {
    return [];
  }
});

const areaList = computed(() => {
  const list = props.cityArea;
  console.log(22,list);
  const areaArr = queryParams.value.reduce((arr: number[], item) => {
    const ar = item.area.map((it) => it);
    arr.push(...ar);
    return arr;
  }, []);
  const groups = list?.reduce(
    (
      group: {
        [key: number]: Array<BusinessDistrictOutput & { selected?: boolean }>;
      },
      item
    ) => {
      const districtDictId = item.districtDictId as number;
      const selected = areaArr.includes(item.key as number);
      if (!group[districtDictId]) {
        group[districtDictId] = [];
      }
      group[districtDictId].push({ ...item, selected });
      return group;
    },
    {}
  );
  return groups;
});

const selectCity = (keywordId: number | undefined) => {
  const list = queryParams.value;
  console.log(66, keywordId, list);
  if (keywordId === 0) {
    //contrlAreaClear()
    clearAll();
    return;
  }
  const resultIndex = list.findIndex((it) => it.cityId === keywordId);
  if (resultIndex > -1 && areaActiveId.value !== keywordId) {
    areaActiveId.value = keywordId;
    return;
  }
  if (resultIndex > -1) {
    list.splice(resultIndex, 1);
    areaActiveId.value = undefined;
  } else {
    list.push({ cityId: keywordId || 0, area: [] });
    areaActiveId.value = keywordId;
  }

  // TODO: 重新启用区域查询功能
  const queryArray = list.map((it) => {
    const areaStr = it.area?.length > 0 ? it.area?.join("_") : "";
    return !!areaStr ? `${it.cityId}:${areaStr}` : it.cityId.toString();
  });
  areaBusiness.value = queryArray.join(",");
  // contrlArea(queryArray.join(","));
  nextTick(() => {
    if (areaRef.value && areaRef.value.length > 0) {
      const height = areaRef.value[0].clientHeight;
      areaShowExpend.value = Number(height) > 45;
    }
  });
};

const selectArea = (item: BusinessDistrictOutput) => {
  const list = queryParams.value;
  console.log(55, list);
  const city = list.find((it) => it.cityId == item.districtDictId);
  if (!city) return;
  if (item.key == 0) {
    if (city.area.length == 0) return;
    city.area = [];
  } else {
    const areaIndex = city.area.findIndex((it) => it == item.key);
    if (areaIndex > -1) {
      city.area.splice(areaIndex, 1);
    } else {
      city.area.push(item.key as number);
    }
  }

  // TODO: 重新启用区域查询功能
  const queryArray = list.map((it) => {
    const areaStr = it.area?.length > 0 ? it.area?.join("_") : "";
    return !!areaStr ? `${it.cityId}:${areaStr}` : it.cityId.toString();
  });
  areaBusiness.value = queryArray.join(",");
};

const removeArea = (item: { id: number; name: string; key: number }) => {
  const list = queryParams.value;
  const cityIndex = list.findIndex((it) => it.cityId == item.id);
  if (cityIndex < 0) return;
  if (item.key == 0) {
    list.splice(cityIndex, 1);
  } else {
    const areaIndex = list[cityIndex].area.findIndex((it) => it == item.key);
    if (areaIndex < 0) return;
    list[cityIndex].area.splice(areaIndex, 1);
  }

  // TODO: 重新启用区域查询功能
  const queryArray = list.map((it) => {
    const areaStr = it.area?.length > 0 ? it.area?.join("_") : "";
    return !!areaStr ? `${it.cityId}:${areaStr}` : it.cityId.toString();
  });
  areaBusiness.value = queryArray.join(",");
};
const clearAll = () => {
  areaBusiness.value = "";
  areaActiveId.value = 0;
};
</script>

<template>
  <div class="area-box">
    <div class="area-select-wrapper">
      <div class="area-select-container" v-if="cityList.length > 0">
        <ul class="dropdown-area-list">
          <li
            v-for="item in cityList"
            :class="{ active: areaActiveId == item.keywordID || item.count }"
            :key="item.keywordID"
            @click="selectCity(item.keywordID)"
          >
            {{ item.keywordName }}
            <em v-if="item.count">({{ item.count }})</em>
          </li>
        </ul>
      </div>
      <template v-for="(item, key) in areaList" :key="`area-${key}`">
        <div
          class="area-select-container has-expand clearfix"
          :class="{ 'is-open': openExpend }"
          v-if="areaActiveId == key"
        >
          <a
            href="javascript:;"
            class="expand-btn"
            v-if="areaShowExpend"
            @click="openExpend = !openExpend"
            >{{ openExpend ? "收起" : "展开全部" }}
            <i
              :class="`${openExpend ? 'i-ep-caret-top' : 'i-ep-caret-bottom'}`"
            ></i
          ></a>
          <ul class="dropdown-area-list" ref="areaRef">
            <li
              v-if="item.length > 0"
              @click="selectArea({ districtDictId: key, key: 0 })"
            >
              不限
            </li>
            <li
              v-for="it in item"
              :key="it.key"
              :class="{ active: it.selected }"
              @click="selectArea(it)"
            >
              {{ it.name }}
            </li>
          </ul>
        </div>
      </template>
    </div>
    <div class="selected-area-section" v-if="querySelectName.length > 0">
      <ul>
        <li v-for="item in querySelectName" :key="item.id">
          <span class="selected-area-name">{{ item.name }}</span>
          <p class="selected-area-list" v-if="item.area.length > 0">
            <a href="javascript:;" v-for="it in item.area" :key="it.key"
              >{{ it.name }}
              <i
                class="i-ep-close icons"
                @click="removeArea({ id: item.id, ...it })"
              ></i
            ></a>
          </p>
        </li>
      </ul>
      <a href="javascript:;" class="clear-area-btn" @click="clearAll"
        >清空已选位置</a
      >
    </div>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #3b86f6;
ul,
li {
  padding: 0;
  margin: 0;
  list-style: none;
}
.area-box {
  .area-select-wrapper {
    // background: #f5f6f9;
    padding: 0 12px;
    border-radius: 4px;
    position: relative;
    .area-select-container {
      padding-top: 10px;
      .dropdown-area-list {
        li {
          display: inline-block;
          white-space: nowrap;
          font-size: 14px;
          font-weight: 400;
          color: #333;
          line-height: 20px;
          cursor: pointer;
          margin: 0 5px 10px;
          padding: 3px 8px;
          &:hover,
          &.active {
            color: #fff;
            background: #2878ff;
            border-radius: 20px 20px 20px 20px;
            
          }
          em{
            font-style: normal;
          }
        }
      }
      & + .area-select-container {
        border-top: 1px solid #ececec;
      }
      &.has-expand {
        max-height: 43px;
        overflow: hidden;
        .expand-btn {
          position: relative;
          float: right;
          font-size: 14px;
          font-weight: 400;
          color: $active-color;
          line-height: 20px;
          z-index: 1;
          padding-right: 15px;
          transition: all 0.2s linear;
        }
      }
      &.is-open {
        max-height: none;
      }
    }
  }
  .selected-area-section {
    position: relative;
    overflow: hidden;
    padding-right: 90px;
    margin-top: 9px;

    ul {
      float: left;
      li {
        float: left;
        margin-right: 8px;
        .selected-area-name {
          font-size: 14px;
          color: #999;
          line-height: 20px;
          float: left;
          margin-top: 12px;
        }
        .selected-area-list {
          float: left;
          a {
            float: left;
            white-space: nowrap;
            background: #f5f6fb;
            border-radius: 2px;
            font-size: 14px;
            color: $active-color;
            line-height: 20px;
            padding: 3px 10px;
            margin-right: 8px;
            margin-top: 8px;
            cursor: default;
            .icons {
              border-radius: 100%;
              cursor: pointer;
              padding: 2px;
              transition: all 0.2s linear;
              font-size: 10px;
              &:hover {
                background: $active-color;
                color: #fff;
              }
            }
          }
        }
      }
      p {
        padding: 0;
        margin: 0;
      }
    }
    .clear-area-btn {
      position: absolute;
      top: 12px;
      right: 0;
      font-size: 14px;
      font-weight: 400;
      color: #999;
      line-height: 20px;
      transition: all 0.2s linear;
    }
  }
}
</style>
