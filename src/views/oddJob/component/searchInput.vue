<script setup lang="ts">
import {
  ApplicationPlatform,
  AutoCompleteApi,
  PositionInputPromptEntity,
} from "@/api-services";
import { feature, getPcAPI } from "@/utils/axios-utils";
import { useDebounceFn, useThrottleFn } from "@vueuse/core";
import { useCityStore } from "@/stores/cityStore";
import SelectPosition from "@/components/common/SelectPosition.vue";
import SelectIndustry from "@/components/common/SelectIndustry.vue";

const cityStore = useCityStore();

console.log(cityStore.cityInfo);
const districtId = computed(() => cityStore.cityInfo.id);
const positionType = ref('');
const industryType = ref('');
const historyList = useLocalStorage<Array<string>>("_Search_History", []);
const schType = ref(1);
const keyword = ref("");
const searchInputBox = ref<any>(null);
const focus = ref(false);


const suggestList = ref<Array<PositionInputPromptEntity & { html: string }>>(
  []
);


const searchSuggest = useThrottleFn(async (event) => {
  //console.log(event.target.value);
  if (schType.value != 1) return;
  const value = event.target.value;

  const [err, res] = await feature(
    getPcAPI(AutoCompleteApi).apiAutoCompleteMatchGet(
      value,
      0,
      10,
      districtId.value,
      ApplicationPlatform.PC
    )
  );
  if (!err && res.data.code === 1) {
    let regex = new RegExp(`(${value})`, "g");
    suggestList.value =
      res.data?.data
        ?.map((item) => {
          return {
            ...item,
            html: (item.text || "").replace(
              regex,
              (match) => `<span style='color:#3B86F6;'>${match}</span>`
            ),
          };
        })
        .filter((item) => item.text != "") || [];
  } else {
    suggestList.value = [];
  }
}, 300);

const selectKeyword = useDebounceFn((text: string) => {
  keyword.value = text;
  focus.value = false;
  search();
}, 200);

//onClickOutside(searchInputBox, () => (focus.value = false));
const clearHistory = () => {
  historyList.value = [];
};

const search = () => {
  //contrlKeyword(keyword.value, schType.value.toString());
  const index = historyList.value.findIndex((item) => item == keyword.value);
  if (index < 0 && schType.value == 1 && keyword.value != "") {
    if (historyList.value.length >= 10) {
      historyList.value.shift();
    }
    historyList.value.push(keyword.value);
  }
};

const handleTypeChange = (type: string) => {
  schType.value = parseInt(type);
};
</script>

<template>
  <div class="position-search-box clearfix">
    <div class="position-search-form">
      <div class="search-selector">
        <el-dropdown @command="handleTypeChange" trigger="click">
          <div class="selector-content">
            <span>{{ schType === 1 ? "职位" : "公司" }}</span>
            <i class="i-ep-arrow-down text-xs ml-1"></i>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="1">找工作</el-dropdown-item>
              <el-dropdown-item command="2">找公司</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div
        class="select-input-box"
        :class="{ wt: schType == 2 }"
        ref="searchInputBox"
      >
        <div class="input-wrapper">
          <input
            type="text"
            v-model="keyword"
            @input="searchSuggest"
            @focus="focus = true"
            placeholder="请输入职位或公司名称或产业园区名称"
            class="input"
            @keyup.enter="search"
          />
        </div>
        <div
          class="related-result-wrapper"
          :class="{
            'history-box':
              suggestList.length == 0 && historyList.length > 0 && schType == 1,
          }"
          v-if="
            (suggestList.length > 0 ||
              (historyList.length > 0 && schType == 1)) &&
            focus
          "
        >
          <ul v-if="suggestList.length > 0">
            <li
              v-for="(item, index) in suggestList"
              :key="index"
              @click="selectKeyword(item.text || '')"
            >
              <span v-html="item.html"></span>
            </li>
          </ul>
          <div
            class="history-group"
            v-else-if="historyList.length > 0 && schType == 1"
          >
            <span class="history-group-title">
              历史搜索
              <a
                href="javascript:;"
                class="clear-search-btn"
                @click="clearHistory"
                >清空历史</a
              >
            </span>
            <div class="history-group-list">
              <a
                href="javascript:;"
                v-for="(item, index) in historyList"
                :key="index"
                @click="selectKeyword(item)"
                >{{ item }}</a
              >
            </div>
          </div>
          <div v-else></div>
        </div>
      </div>
      <select-position
        v-model="positionType"
        title="期望职位"
        v-if="schType == 1"
      />
      <select-industry v-model="industryType" title="期望行业" />

      <a class="search-btn" @click="search">搜索</a>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.position-search-box {
    background: #007bf6;
    padding: 2px;
    border-radius: 4px;
}
.position-search-form {
  display: flex;
  align-items: center;
  background: transparent;
  border-radius: 4px;
  overflow: hidden;
  height: 46px;

  .br {
    border-right: 1px solid #e8e8e8;
  }
  .search-selector {
    line-height: 46px;
    height: 46px;
    flex-shrink: 0;
    padding: 0 20px;
    border-right: 1px solid #E8E8E8;
    cursor: pointer;
    background: white;
    border-radius: 4px 0 0 4px;

    .selector-content {
      display: flex;
      align-items: center;
      color: #333;
      font-size: 14px;
      white-space: nowrap;

      &:hover {
        color: #007bf6;
      }
    }
  }
  :deep(.el-dropdown) {
    width: 100%;
    height: 100%;

    .el-dropdown__caret-button {
      display: none;
    }
  }
  .select-input-box {
    position: relative;
    float: left;
    width: 630px;
    border-right: 1px solid #e8e8e8;
    .input-wrapper {
      width: 100%;
      color: #222;

      .input {
        width: 100%;
        height: 48px;
        line-height: 22px;
        padding-left: 23px;
        box-sizing: border-box;
        border: none;
      }
    }
    .related-result-wrapper {
      position: absolute;
      top: 54px;
      left: 0;
      width: 628px;
      background: #fff;
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
      z-index: 6;
      border-radius: 12px;
      padding: 4px 0;
      ul {
        max-height: 400px;
        overflow: overlay;
        li {
          line-height: 40px;
          padding: 0 16px;
          cursor: pointer;
          color: #333;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: all 0.2s linear;
          &:hover {
            background: #f8f8f8;
          }
        }
      }
    }
    .history-box {
      padding: 4px 16px 16px;
      .history-group {
        position: relative;
        margin-top: 8px;
        &-title {
          display: block;
          font-size: 12px;
          color: #757575;
          line-height: 17px;
          padding: 8px 0;
          .clear-search-btn {
            float: right;
            font-size: 12px;
            color: #999;
            line-height: 17px;
            transition: all 0.2s linear;
          }
        }
        &-list {
          overflow: hidden;
          margin-left: -6px;
          margin-right: -6px;
          a {
            display: flex;
            align-items: center;
            background: #f8f8f8;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
            line-height: 20px;
            padding: 2px 8px;
            float: left;
            margin: 4px 6px;
            word-break: break-all;
            transition: all 0.2s linear;
            &:hover {
              color: #fff;
              background: #3b86f6;
            }
          }
        }
      }
    }
  }
  .search-btn {
    display: block;
    float: left;
    width: 105px;
    height: 48px;
    font-size: 16px;
    line-height: 48px;
    text-align: center;

    color: #fff;
    cursor: pointer;
  }
  :deep(.el-input__inner) {
    border: none;
    height: 48px;
    line-height: 48px;
  }
}
</style>
