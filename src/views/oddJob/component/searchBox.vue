<script setup lang="ts">
import { useCityStore } from "@/stores/cityStore";
import SearchInput from "./searchInput.vue";
import SelectCityWrapper from "./SelectCityWrapper.vue";
import CityArea from "./CityArea.vue";
import { feature, getPcAPI } from "@/utils/axios-utils";
import { BusinessDistrictOutput, DataApi, KeywordItemDto } from "@/api-services";

const cityStore = useCityStore();
const districtId = computed(() => cityStore.cityInfo.id);
const cityId = ref(cityStore.cityInfo.cityId);
const cityName = ref('');
const tabActiveIndex = ref(1);
const schType = ref(1);
const cityList = ref<Array<KeywordItemDto>>([]);

const cityAreaList = ref<Array<BusinessDistrictOutput>>([]);
const cityAreaSelect = ref(null);
const cityAreaSelectVisible = ref(false);
const showCityWrapper = ref(false);


const areaCity = computed(() =>
  cityList.value.filter((item) => item.parentID == cityId.value)
);
onMounted(async () => {
  //cityId.value = districtId.value;
  cityName.value = cityStore.cityInfo.name;
  await cityInit();
  await cityAreaInit();
});
watch(()=>cityId.value, async (newval) => {
  console.log(11,newval);
  await cityAreaInit();
});

const cityInit = async () => {
  const [err, res] = await feature(
    getPcAPI(DataApi).apiDataDistrictGet(0, true, districtId.value, 0)
  );
  if (!err && res.data.code == 1) {
    cityList.value = res.data?.data || [];
    cityList.value = cityList.value.map(item=>{
      if(item.keywordName == '广西壮族自治区'){
        item.keywordName = '广西'
      }
      return item
    })
  }
};
const cityAreaInit = async () => {
  const [err, res] = await feature(
    getPcAPI(DataApi).apiDataBusinessDistrictsByCityCityDictIdGet(cityId.value)
  );
  if (!err && res.data.code == 1) {
    cityAreaList.value = res.data?.data || [];
  }
};
const changeTabIndex = (index: number) => {
  if (index == 0) {
    showCityWrapper.value = true;
  }
  tabActiveIndex.value = index;
};
const handleComfirmCity = (value: number) => {
  cityId.value = value;
  showCityWrapper.value = false;
};
</script>

<template>
  <div class="rounded-8px p-20px my-20px bg-#F1F7FB">
    <SearchInput />
    <div class="city-area-select" ref="cityAreaSelect">
      <div class="city-area-dropdown">
        <div class="position-search-location">
          <div class="search-location-item area" @click="changeTabIndex(0)">
            <span class="text">{{ cityName }}</span>
            <i class="icon i-ep-arrow-up"></i>
          </div>
          <div
            class="search-location-item"
            :class="{ active: tabActiveIndex == 1 }"
            @click="changeTabIndex(1)"
            v-if="schType == 1"
          >
            <span class="text">区域选择</span
            ><i class="icon i-ep-arrow-up mb-5px"></i>
          </div>
        </div>
        <template v-if="tabActiveIndex == 1 && schType == 1">
          <city-area :city="areaCity" :cityArea="cityAreaList" />
        </template>
      </div>
    </div>
  </div>
  <select-city-wrapper
    v-model="showCityWrapper"
    :city="cityList"
    @close="showCityWrapper = false"
    @comfirm="handleComfirmCity"
  />
</template>

<style lang="scss" scoped>
$active-color: #3b86f6;
.city-area-select {
  position: relative;
  &.pick-up {
    display: inline-block;
    height: 32px;
    overflow: hidden;

    .city-area-dropdown {
      display: none;
      position: absolute;
      width: 1184px;
      top: 40px;
      left: 0;
      z-index: 4;
      padding: 16px;
      background: #fff;
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
      border-radius: 8px;
      border: 1px solid #ededed;

      .area {
        display: none;
      }
    }
    .city-current {
      display: block;
    }
  }
  &.pick-up:hover {
    overflow: visible;
    .city-area-dropdown {
      display: block;
    }
    &::after {
      content: " ";
      position: absolute;
      left: 0;
      right: 0;
      bottom: -8px;
      height: 8px;
      background: transparent;
      z-index: 4;
    }
  }
  .city-current {
    display: none;
    position: relative;
    background: #f2f3f5;
    color: $active-color;
    font-weight: 500;
    border-radius: 4px;
    font-size: 14px;
    line-height: 20px;
    padding: 6px 12px;
    cursor: pointer;
    transition: all 0.2s linear;
  }
  .position-search-location {
    height: 41px;
    .search-location-item {
      display: inline-block;
      width: 100px;
      height: 41px;
      line-height: 41px;
      text-align: center;
      font-size: 14px;
      color: #34495e;
      cursor: pointer;
      &.area {
        padding-right: 22px;
      }
      .text {
        padding-right: 8px;
      }
      &.active {
        color: $active-color;
        .icon {
          color: $active-color;
          transform: rotateZ(0deg);
        }
      }
      .icon {
        color: #d2d2d2;
        //padding-left: 8px;
        transform: rotateZ(180deg);
        transition: transform 0.3s;
      }
    }
  }
}
</style>
