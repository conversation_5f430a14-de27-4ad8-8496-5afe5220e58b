<script setup lang="ts">
import {
  ApplicationPlatform,
  AutoCompleteApi,
  DataApi,
  KeywordItemDto,
} from "@/api-services";
import { feature, getPcAPI } from "@/utils/axios-utils";
import { useCityStore } from "@/stores/cityStore";

type keywordNewDto = KeywordItemDto & {
  fullName?: string;
  selected?: boolean;
};

const props = withDefaults(
  defineProps<{
    title: string;
  }>(),
  {
    title: "",
  }
);
const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
}>();
const cityStore = useCityStore();

const districtId = computed(() => cityStore.cityInfo.id);

//const { contrlIndustry, query } = useRouteConfig();
const keyword = ref("");
const firstactiveIndex = ref<number | undefined>();
const visiable = ref(false);
const industryOptionList = ref<Array<KeywordItemDto>>([]);
const searchKeywordList = ref<Array<keywordNewDto>>();

const selectList = ref<Array<keywordNewDto>>([]);

const comfirmList = ref<Array<keywordNewDto>>([]);

const firtstLevel = computed(() => {
  if (!firstactiveIndex.value)
    firstactiveIndex.value = industryOptionList.value[0]?.keywordID;
  return industryOptionList.value.filter((item) => item.parentID == 0);
});
const secondLevel = computed(() => {
  const arr = industryOptionList.value
    .filter((item) => item.parentID == firstactiveIndex.value)
    .map((item) => {
      return { ...item, fullName: "" };
    }) as keywordNewDto[];
  const newArr = arr.map((item) => {
    const selected = selectList.value?.find(
      (it) => it.keywordID == item.keywordID
    );
    return {
      ...item,
      fullName: item.fullName ? item.fullName : item.keywordName,
      selected: !!selected,
    }as keywordNewDto;
  });
  return newArr;
});

const selectStr = computed(() => {
  if (comfirmList.value?.length > 0) {
    return comfirmList.value.map((item) => item.fullName).join(",");
  }
  return "选择行业";
});

const industryOptionsInit = async () => {
  const [err, res] = await feature(
    getPcAPI(DataApi).apiDataIndustryGet(
      0,
      true,
      districtId.value,
      ApplicationPlatform.PC
    )
  );
  if (!err && res.data.code == 1) {
    //console.log(res.data.data);
    industryOptionList.value = res.data?.data || [];
  }
};

const selectFirstLevel = (keywordID: number | undefined) => {
  firstactiveIndex.value = keywordID;
  //secondactiveIndex.value = undefined;
};

const selectThirdLevel = (item: keywordNewDto) => {
  const index = selectList.value?.findIndex(
    (it) => it.keywordID == item.keywordID
  );
  if (index > -1) {
    selectList.value?.splice(index, 1);
  } else {
    if (selectList.value?.length >= 3 && item.keywordID != 10472) {
      return;
    }

    // if (item.keywordID == firstactiveIndex.value) {
    //   selectList.value = selectList.value?.filter(
    //     (it) => it.parentID !== firstactiveIndex.value
    //   );
    // } else {
    //   const parentIndex = selectList.value?.findIndex(
    //     (it) => it.keywordID == item.parentID
    //   );
    //   if (parentIndex > -1) {
    //     selectList.value?.splice(parentIndex, 1);
    //   }
    // }
    if (item.keywordID == 10472) {
      selectList.value = [];
    } else {
      const i = selectList.value?.findIndex((it) => it.keywordID == 10472);
      if (i > -1) {
        selectList.value?.splice(i, 1);
      }
    }
    selectList.value?.push(item);
  }
};

const searchRemote = async (text: string) => {
  const [err, res] = await feature(
    getPcAPI(AutoCompleteApi).apiAutoCompleteIndustryGet(
      text,
      10,
      0,
      0,
      ApplicationPlatform.PC
    )
  );
  if (!err && res.data.code == 1) {
    searchKeywordList.value = res.data?.data?.map((item) => {
      return {
        keywordID: item.keywordID ? Number(item.keywordID) : undefined,
        keywordName: item.keywordName || undefined,
        fullName: item.keywordName || "",
        parentID: item.keywordParentID || undefined,
        sort: undefined,
        hasNext: undefined,
        blueCollarFlag: undefined,
      } as keywordNewDto;
    });
  } else {
    searchKeywordList.value = [];
  }
};

const beforeClose = (done: () => {}) => {
  selectList.value = comfirmList.value.map((item) => item);
  done();
};

const confirmSelect = () => {
  comfirmList.value = selectList.value.map((item) => item);
  const str = comfirmList.value.map((item) => item.keywordID).join(",") || "";
  emit("update:modelValue", str);
  visiable.value = false;
};

const change = () => {
  keyword.value = "";
  //event.preventDefault();
};

onMounted(async () => {
  await industryOptionsInit();
});
</script>

<template>
  <div class="keyword-list-wrapper">
    <div class="select-input" @click="visiable = true">
      {{ selectStr }}
      <i class="i-ep-arrow-down text-12px"></i>
    </div>
    <el-dialog
      v-model="visiable"
      :append-to-body="true"
      :before-close="beforeClose"
      width="845px"
    >
      <template #title>
        <label class="tit">选择{{ `${title}(${selectList.length}/3)` }}</label>
        <el-select
          class="sele-box"
          v-model="keyword"
          filterable
          placeholder="请输入关键词"
          remote
          :remote-method="searchRemote"
          @change="change"
        >
          <template #prefix>
            <i class="iconfont icon-search1"></i>
          </template>
          <el-option
            v-for="item in searchKeywordList"
            :key="item.keywordID"
            class="sele-li"
            :value="item.keywordName"
            @click.native.prevent="selectThirdLevel(item)"
          >
            <p class="bod">{{ item.keywordName }}</p>
          </el-option>
        </el-select>
        <a href="javascript:;" class="comfirm-btn" @click="confirmSelect"
          >确定</a
        >
      </template>
      <div class="select-keyword clearfix" v-if="selectList.length > 0">
        <ul>
          <li class="list-none" v-for="item in selectList" :key="item.keywordID">
            {{ item.fullName }}
            <i
              data-v-03e98dc2=""
              class="i-ep-close text-14px cursor-pointer"
              @click="selectThirdLevel(item)"
            ></i>
          </li>
        </ul>
      </div>
      <div class="sel-box clearfix">
        <div class="firstBox tBox float-left">
          <ul>
            <li
              v-for="item in firtstLevel"
              :class="{ on: firstactiveIndex === item.keywordID }"
              :key="item.keywordID"
              @click="selectFirstLevel(item.keywordID)"
            >
              <span>{{ item.keywordName }}</span>
            </li>
          </ul>
        </div>

        <div class="thirdBox tBox float-left">
          <ul>
            <li
              v-for="item in secondLevel"
              :key="item.keywordID"
              :title="item.keywordName || ''"
              :class="{ on: item.selected }"
              @click="selectThirdLevel(item)"
            >
              <span>{{ item.keywordName }}</span>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
$active-color: #3b86f6;
.select-input {
  float: left;
  width: 138px;
  background: #fff;
  cursor: pointer;
  height: 48px;
  line-height: 48px;
  text-align: center;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 10px;
  border-right: 1px solid #e8e8e8;
}
.el-dialog__header {
  border-bottom: 1px solid #f2f2f2;
  label.tit {
    font-size: 20px;
    color: #333;
  }
}

.sele-box {
  width: 450px;
  margin-left: 20px;
  border-radius: 4px;

  .bod {
    color: #457ccf;
    font-size: 14px;
  }
}

.iconfont {
  padding-left: 4px;
}
.comfirm-btn {
  // position: absolute;
  // top: 6px;
  // right: 26px;
  margin-left: 10px;
  padding: 8px 16px;
  background: $active-color;
  color: #fff;
  border-radius: 4px;
}
:deep(.el-select__wrapper) {
  background: #fafafa;
  box-shadow: none !important;
  line-height: 32px;
  min-height: 40px;
}
:deep(.el-icon-arrow-up) {
  display: none;
}
:deep(.el-input__prefix) {
  top: 7px;
}
:deep(.el-input__inner) {
  width: 420px !important;
  background: #fafafa;
  color: #333;
  font-size: 14px;
  border: none;
  border-radius: 4px;
}
:deep(.el-dialog__body) {
  padding: 0 0 0 0;
}
:deep(.el-dialog__headerbtn .el-dialog__close) {
  font-size: 30px;
  color: #d4d4d4;
}
.select-keyword {
  position: relative;
  overflow: hidden;
  margin: 0 16px 10px 0;
  ul {
    float: left;
    padding-right: 80px;
    padding-left: 0;
    li {
      float: left;
      white-space: nowrap;
      background: #f5f6fb;
      border-radius: 2px;
      font-size: 14px;
      color: $active-color;
      padding: 3px 10px;
      margin-right: 8px;
      margin-top: 8px;
      cursor: default;
      .icons {
        border-radius: 100%;
        cursor: pointer;
        padding: 2px;
        transition: all 0.2s linear;
        &:hover {
          background: $active-color;
          color: #fff;
        }
      }
    }
  }
}
.sel-box {
  height: 500px;
  overflow: hidden;
  ul,
  li {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .tBox {
    width: 160px;
    height: 500px;
    position: relative;
    overflow: hidden;
    ul {
      position: absolute;
      left: 0;
      top: 0;

      bottom: 0;
      overflow-x: hidden;
      overflow-y: scroll;
    }
    li {
      font-size: 14px;
      color: #666;
      line-height: 24px;
      cursor: pointer;
      &:hover {
        color: $active-color;
      }
    }
    li.on {
      color: $active-color;
    }
  }
  // .tBox ::-webkit-scrollbar {
  //   width: 0 !important;
  // }
  .tBox {
    -ms-overflow-style: none;
  }
  .tBox {
    overflow: -moz-scrollbars-none;
  }
  .firstBox {
    background: #ecedf4;
    li {
      padding: 18px 15px 18px 24px;
      background: #ecedf4;
    }
    li.on {
      background: #f4f5f9;
    }
  }
  .secondBox {
    background: #f4f5f9;
    li {
      padding: 18px 15px 18px 24px;
      background: #f4f5f9;
      width: 160px;
    }
    li.on {
      background: #ffffff;
    }
  }
  .thirdBox {
    width: 650px;
    li {
      float: left;
      background: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 18px 5px 18px 12px;
      width: 142px;
    }
  }
}
</style>
