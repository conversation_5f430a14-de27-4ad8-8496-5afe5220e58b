<template>
  <div class="city-selector">
    <el-dropdown @command="handleCityChange" trigger="click">
      <div class="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-lg cursor-pointer transition-all">
        <i class="i-ep-location text-primary-500"></i>
        <span class="text-sm font-medium text-gray-700">
          {{ cityStore.cityInfo.name }}
        </span>
        <i class="i-ep-arrow-down text-gray-400 text-xs"></i>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item 
            v-for="city in cityStore.availableCities" 
            :key="city.code"
            :command="city.code"
            :disabled="city.code === cityStore.currentCity"
          >
            <div class="flex items-center justify-between w-full">
              <span>{{ city.name }}</span>
              <i 
                v-if="city.code === cityStore.currentCity" 
                class="i-ep-check text-primary-500 ml-2"
              ></i>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup lang="ts">
import { useCityStore } from '@/stores/cityStore'
import { ElMessage } from 'element-plus'
import { goToCity } from '@/router'
import type { CityCode } from '@/types'

// Store
const cityStore = useCityStore()

// 处理城市切换
const handleCityChange = (cityCode: CityCode) => {
  if (cityCode === cityStore.currentCity) return

  const success = goToCity(cityCode)
  if (success) {
    ElMessage.success(`已切换到${cityStore.getCityByCode(cityCode)?.name}`)
  } else {
    ElMessage.error('城市切换失败')
  }
}
</script>

<style scoped lang="scss">
.city-selector {
  .el-dropdown {
    outline: none;
  }
}
</style>