<script setup lang="ts">
import { useRouter } from "vue-router";
import { useCityStore } from "@/stores/cityStore";
import { ElMessage } from "element-plus";

const router = useRouter();
const cityStore = useCityStore();


const searchKeyword = ref("");
const searchType = ref(1);

// 热门搜索标签
const hotSearchTags = ref([
  "会计",
  "销售",
  "人事",
  "司机",
  "文员",
  "JAVA工程师",
  "主治医师",
  "设计师",
  "物流",
  "中介",
  "操作员",
  "教师",
]);


// 搜索处理
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning("请输入搜索关键词");
    return;
  }
  console.log(
    "搜索关键词:",
    searchKeyword.value,
    "搜索类型:",
    searchType.value
  );
  // TODO: 实现搜索逻辑
  ElMessage.success(`搜索关键词: ${searchKeyword.value}`);
};

// 处理搜索类型变化
const handleTypeChange = (type: string) => {
  searchType.value = parseInt(type);
};
</script>
<template>
  <div class="container-lg">
    <div class="flex items-center justify-between">
      <!-- Logo 和标题 -->
      <div class="flex items-center space-x-4">
        <router-link
          :to="cityStore.getCityHomePath"
          class="flex items-center space-x-3 hover:opacity-80 transition-opacity"
        >
          <el-image
            src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
            class="w-265px h-72px"
          />
        </router-link>
      </div>

      <!-- 中间搜索区域 -->
      <div class="flex-1 max-w-2xl mx-8">
        <div class="search-container">
          <div class="search-wrapper">
            <!-- 左侧选择器 -->
            <div class="search-selector">
              <el-dropdown @command="handleTypeChange" trigger="click">
                <div class="selector-content">
                  <span>{{ searchType === 1 ? "找工作" : "找公司" }}</span>
                  <i class="i-ep-arrow-down text-xs ml-1"></i>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="1">找工作</el-dropdown-item>
                    <el-dropdown-item command="2">找公司</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <!-- 中间输入框 -->
            <div class="search-input-wrapper">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="输入您想要查询的关键词"
                class="search-input"
                @keyup.enter="handleSearch"
              />
            </div>
            <!-- 右侧搜索按钮 -->
            <button class="search-btn" @click="handleSearch">搜索</button>
          </div>
        </div>
        <!-- 热门搜索 -->
        <div class="hot-search mt-12px ml-30px text-sm text-#666">
          热门搜索：
          <span
            v-for="tag in hotSearchTags"
            :key="tag"
            class="hot-tag"
            @click="
              searchKeyword = tag;
              handleSearch();
            "
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- 右侧功能区 -->
      <div class="flex items-center space-x-4">
        <!-- 二维码 -->
        <div class="qr-code-section">
          <div class="text-center">
            <el-image
              src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
              class="w-92px h-95px"
            />
          </div>
          <div class="qr-text text-xs text-center text-#2C3137">
            <div>扫码查看“广西就业”小程序</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 搜索框样式
.search-container {
  background: #007bf6;
  border-radius: 26px;
  padding: 2px;

  .search-wrapper {
    display: flex;
    align-items: center;
    background: transparent;
    border-radius: 24px;
    overflow: hidden;
    height: 46px;

    .search-selector {
      line-height: 46px;
      height: 46px;
      flex-shrink: 0;
      padding: 0 20px;
      border-right: 1px solid #e5e7eb;
      cursor: pointer;
      background: white;
      border-radius: 24px 0 0 24px;

      .selector-content {
        display: flex;
        align-items: center;
        color: #333;
        font-size: 14px;
        white-space: nowrap;

        &:hover {
          color: #007bf6;
        }
      }
    }

    .search-input-wrapper {
      flex: 1;
      height: 100%;
      background: white;

      .search-input {
        width: 100%;
        height: 100%;
        border: none;
        outline: none;
        padding: 0 20px;
        font-size: 14px;
        background: transparent;
        box-sizing: border-box;

        &::placeholder {
          color: #999;
        }

        &:focus {
          outline: none;
        }
      }
    }

    .search-btn {
      flex-shrink: 0;
      background-color: #007bf6;
      color: white;
      border: none;
      padding: 0 43px;
      height: 100%;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      border-radius: 0 24px 24px 0;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #0056cc;
      }

      &:active {
        background-color: #004bb3;
      }
    }
  }
}

// Element Plus Dropdown 样式覆盖
:deep(.el-dropdown) {
  width: 100%;
  height: 100%;

  .el-dropdown__caret-button {
    display: none;
  }
}

// 热门搜索样式
.hot-search {
  .hot-tag {
    color: #666666;
    cursor: pointer;
    margin-left: 4px;
    margin-right: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s ease;

    &:hover {
      color: #0056cc;
    }
  }
}
</style>
