<template>
  <!-- 顶部区域 -->
  <div class="bg-white border-b">
    <div class="container-lg py-2">
      <div class="flex items-center justify-between text-sm text-gray-600">
        <div class="flex items-center space-x-4">
          <CitySelector class="hidden md:block" />
          <span>您好，欢迎来到广西壮族自治区"数智人社"—广西就业平台！</span>
        </div>
        <div class="flex items-center space-x-4">
          <div
            class="flex items-center space-x-2 bg-#E4E7F1 rounded-20px px-15px py-7px cursor-pointer"
          >
            <img
              src="//image.gxrc.com/thirdParty/gxjy/pc/home/<USER>"
              class="w-21px h-21px"
              alt=""
            />
            <span>AI智能客服</span>
          </div>
          <!-- 登录按钮组 -->
          <div class="flex items-center space-x-2">
            <div class="login-btn enterprise">企业登录</div>
            <div class="login-btn personal">个人登录</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CitySelector from '@/components/common/CitySelector.vue'
</script>

<style scoped lang="scss">
.login-btn {
  @apply cursor-pointer rounded-20px text-white text-14px px-23px py-7px;
}
.enterprise {
  background: linear-gradient(301deg, #0b83fb 0%, #0a23ff 100%);
}
.personal {
  background: linear-gradient(301deg, #00ebc3 0%, #00b0ff 100%);
}
</style>