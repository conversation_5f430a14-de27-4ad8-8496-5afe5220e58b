<template>
  <header class="header">
    <!-- 主头部区域 -->
    <div class="main-header py-4">
      <SearchWrapper />
    </div>

    <!-- 导航菜单栏 -->
    <div class="nav-menu">
      <div class="container-lg">
        <nav class="flex items-center justify-between">
          <div class="flex items-center space-x-0">
            <template v-for="menu in navigationMenus" :key="menu.path">
              <a
                v-if="menu.external"
                :href="menu.path"
                target="_blank"
                class="nav-item"
              >
                {{ menu.name }}
              </a>
              <router-link
                v-else
                :to="cityStore.getCityPagePath(menu.path)"
                class="nav-item"
                :class="{ 'nav-item-active': isMenuActive(menu.path) }"
              >
                {{ menu.name }}
              </router-link>
            </template>
          </div>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useCityStore } from "@/stores/cityStore";
import SearchWrapper from "@/components/common/SearchWrapper.vue";


// Store
const cityStore = useCityStore();

const router = useRouter();

// 组件状态
const showMobileMenu = ref(false);

// 导航菜单配置
const navigationMenus = computed(() => [
  {
    name: "首页",
    path: "",
  },
  {
    name: "用工需求地图",
    path: "demandMap",
  },
  {
    name: "招聘专题",
    path: "recruitment",
  },
  {
    name: "区外招聘",
    path: "external-recruitment",
  },
  {
    name: "粤桂劳务协作",
    path: "labor-cooperation",
  },
  {
    name: "零工市场",
    path: "oddjob",
  },
  {
    name: "职业指导",
    path: "training-guidance",
  },
  {
    name: "创业服务",
    path: "entrepreneurship",
  },
  {
    name: "就业见习",
    path: "https://www.gx12333.net/market/business/website/employinternship/index/index.html",
    external: true,
  },
  {
    name: "服务机构",
    path: "service-agencies",
  },
]);



// 判断菜单是否激活
const isMenuActive = (path: string) => {
  const currentPath = router.currentRoute.value.path;
  return cityStore.isCurrentPath(path)(currentPath);
};

// 监听路由变化，关闭移动端菜单
watch(
  () => router.currentRoute.value.path,
  () => {
    showMobileMenu.value = false;
  }
);

// 点击外部关闭移动端菜单
let clickOutsideHandler: ((event: Event) => void) | null = null;

onMounted(() => {
  clickOutsideHandler = (event: Event) => {
    const target = event.target as Element;
    const header = document.querySelector(".header");
    if (header && !header.contains(target) && showMobileMenu.value) {
      showMobileMenu.value = false;
    }
  };

  // 使用 passive 监听器提升性能
  document.addEventListener("click", clickOutsideHandler, { passive: true });
});

onUnmounted(() => {
  if (clickOutsideHandler) {
    document.removeEventListener("click", clickOutsideHandler);
    clickOutsideHandler = null;
  }
});
</script>

<style scoped lang="scss">
// 导航菜单样式
.nav-menu {
  background: linear-gradient(180deg, #006bd5 0%, #0c86ff 100%);
}

.nav-item {
  display: inline-block;
  padding: 12px 26px;
  color: white;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:hover {
    background: linear-gradient(180deg, #00ebc3 0%, #00b0ff 100%);
    color: white;
  }
}

.nav-item-active {
  background: linear-gradient(180deg, #00ebc3 0%, #00b0ff 100%);
  color: white;
}

.nav-link {
  @apply px-3 py-2 text-gray-700 hover:text-primary-500 hover:bg-gray-100 rounded-lg transition-all font-medium;
  display: flex;
  align-items: center;
}

.nav-link-active {
  @apply text-primary-500 bg-primary-50;
}
</style>
