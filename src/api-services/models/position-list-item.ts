/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionKeywordDto } from './position-keyword-dto';
/**
 * 职位列表项
 * @export
 * @interface PositionListItem
 */
export interface PositionListItem {
    /**
     * 跟踪guid
     * @type {string}
     * @memberof PositionListItem
     */
    trackingGuid?: string | null;
    /**
     * 企业ID
     * @type {number}
     * @memberof PositionListItem
     */
    enterpriseID?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof PositionListItem
     */
    enterpriseGuid?: string;
    /**
     * 职位
     * @type {number}
     * @memberof PositionListItem
     */
    positionID?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionListItem
     */
    positionGuid?: string;
    /**
     * 职位名称
     * @type {string}
     * @memberof PositionListItem
     */
    positionName?: string | null;
    /**
     * 企业名称
     * @type {string}
     * @memberof PositionListItem
     */
    enterpriseName?: string | null;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof PositionListItem
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 薪资待遇
     * @type {string}
     * @memberof PositionListItem
     */
    payPackage?: string | null;
    /**
     * 薪资待遇
     * @type {number}
     * @memberof PositionListItem
     */
    payPackageID?: number;
    /**
     * 工作地
     * @type {string}
     * @memberof PositionListItem
     */
    workPlace?: string | null;
    /**
     * 工作性质
     * @type {string}
     * @memberof PositionListItem
     */
    workProperty?: string | null;
    /**
     * 工作性质ID
     * @type {number}
     * @memberof PositionListItem
     */
    workPropertyID?: number;
    /**
     * 学历
     * @type {string}
     * @memberof PositionListItem
     */
    degreeName?: string | null;
    /**
     * 发布日期
     * @type {Date}
     * @memberof PositionListItem
     */
    publishTime?: Date;
    /**
     * 发布日期描述 今天、昨天、04-22（本年度的不用显示年份）、2021-12-31（非本年度显示年份）
     * @type {string}
     * @memberof PositionListItem
     */
    publishTimeDescribe?: string | null;
    /**
     * 是否接收毕业生
     * @type {boolean}
     * @memberof PositionListItem
     */
    isReceiveGraduate?: boolean;
    /**
     * 值聊
     * @type {boolean}
     * @memberof PositionListItem
     */
    zhiliao?: boolean;
    /**
     * 招聘人数
     * @type {string}
     * @memberof PositionListItem
     */
    positionAmount?: string | null;
    /**
     * 是否急聘
     * @type {boolean}
     * @memberof PositionListItem
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 工龄要求
     * @type {string}
     * @memberof PositionListItem
     */
    workAge?: string | null;
    /**
     * 职位距离
     * @type {string}
     * @memberof PositionListItem
     */
    distance?: string | null;
    /**
     * 部门ID
     * @type {number}
     * @memberof PositionListItem
     */
    departmentId?: number;
    /**
     * 部门名称
     * @type {string}
     * @memberof PositionListItem
     */
    departmentName?: string | null;
    /**
     * 工资福利
     * @type {Array<string>}
     * @memberof PositionListItem
     */
    positionWelfare?: Array<string> | null;
    /**
     * 企业行业
     * @type {string}
     * @memberof PositionListItem
     */
    enterpriseIndustryName?: string | null;
    /**
     * 企业性质
     * @type {string}
     * @memberof PositionListItem
     */
    enterprisePropertyName?: string | null;
    /**
     * 企业性质
     * @type {number}
     * @memberof PositionListItem
     */
    enterpriseProperty?: number;
    /**
     * 企业规模
     * @type {string}
     * @memberof PositionListItem
     */
    enterpriseEmployeeNumberName?: string | null;
    /**
     * 企业Logo
     * @type {string}
     * @memberof PositionListItem
     */
    logoUrl?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionListItem
     */
    requirementOfWorkAge?: number;
    /**
     * 关键词
     * @type {Array<PositionKeywordDto>}
     * @memberof PositionListItem
     */
    positionKeywords?: Array<PositionKeywordDto> | null;
    /**
     * 薪酬计算最小值
     * @type {number}
     * @memberof PositionListItem
     */
    monthPayPackageFrom?: number | null;
    /**
     * 薪酬计算最大值
     * @type {number}
     * @memberof PositionListItem
     */
    monthPayPackageTo?: number | null;
    /**
     * 是否已 投递/申请
     * @type {boolean}
     * @memberof PositionListItem
     */
    isDeliver?: boolean;
    /**
     * 已投递文案
     * @type {string}
     * @memberof PositionListItem
     */
    deliverText?: string | null;
    /**
     * 类型：0:原获取记录 7:曝光职位
     * @type {number}
     * @memberof PositionListItem
     */
    recordType?: number;
    /**
     * 曝光业务表主键
     * @type {number}
     * @memberof PositionListItem
     */
    inviChatRecordId?: number;
    /**
     * 职位描述
     * @type {string}
     * @memberof PositionListItem
     */
    describe?: string | null;
    /**
     * 是否是代招
     * @type {boolean}
     * @memberof PositionListItem
     */
    isAgentRecruit?: boolean | null;
}
