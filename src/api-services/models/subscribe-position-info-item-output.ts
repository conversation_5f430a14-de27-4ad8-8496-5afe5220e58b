/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SubscribeCityOutput } from './subscribe-city-output';
import { SubscribePositionInfoInput } from './subscribe-position-info-input';
import { SubscribeSelectItemOutput } from './subscribe-select-item-output';
/**
 * 
 * @export
 * @interface SubscribePositionInfoItemOutput
 */
export interface SubscribePositionInfoItemOutput {
    /**
     * 主键
     * @type {number}
     * @memberof SubscribePositionInfoItemOutput
     */
    id?: number;
    /**
     * 订阅关键字
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    keyWord?: string | null;
    /**
     * 城市名称
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    workPlaceName?: string | null;
    /**
     * 搜索条件文本
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    searchRequestText?: string | null;
    /**
     * 城市名称 + 搜索条件文本
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    fullSearchRequestText?: string | null;
    /**
     * 搜索条件的json字符串
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    searchRequestString?: string | null;
    /**
     * 
     * @type {SubscribePositionInfoInput}
     * @memberof SubscribePositionInfoItemOutput
     */
    search?: SubscribePositionInfoInput;
    /**
     * 
     * @type {SubscribeCityOutput}
     * @memberof SubscribePositionInfoItemOutput
     */
    workPlace?: SubscribeCityOutput;
    /**
     * 
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    positionCareesText?: string | null;
    /**
     * 
     * @type {Array<SubscribeSelectItemOutput>}
     * @memberof SubscribePositionInfoItemOutput
     */
    positionCarees?: Array<SubscribeSelectItemOutput> | null;
    /**
     * 
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    positionIndustrysText?: string | null;
    /**
     * 
     * @type {Array<SubscribeSelectItemOutput>}
     * @memberof SubscribePositionInfoItemOutput
     */
    positionIndustrys?: Array<SubscribeSelectItemOutput> | null;
    /**
     * 
     * @type {number}
     * @memberof SubscribePositionInfoItemOutput
     */
    workPlaceId?: number | null;
    /**
     * 
     * @type {string}
     * @memberof SubscribePositionInfoItemOutput
     */
    workPlacePath?: string | null;
}
