/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 企业动态主页基本信息输出数据模型
 * @export
 * @interface EnterpriseSocialDynamicsBaseInfoOutput
 */
export interface EnterpriseSocialDynamicsBaseInfoOutput {
    /**
     * 公司名称
     * @type {string}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    name?: string | null;
    /**
     * 公司简介
     * @type {string}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    description?: string | null;
    /**
     * 企业所属行业
     * @type {string}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    enterpriseIndustry?: string | null;
    /**
     * 企业雇员规模
     * @type {string}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    employeeNumber?: string | null;
    /**
     * 企业Logo URL
     * @type {string}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    enterpriseAvatar?: string | null;
    /**
     * 是否为运营账号
     * @type {boolean}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    isOperationAccount?: boolean | null;
    /**
     * 获赞总数
     * @type {number}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    likes?: number;
    /**
     * 收藏总数
     * @type {number}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    collection?: number;
    /**
     * 粉丝数量
     * @type {number}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    fans?: number;
    /**
     * 当前求职者是否已经关注该企业
     * @type {boolean}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    isFollowed?: boolean;
    /**
     * 企业招聘业务是否过期
     * @type {boolean}
     * @memberof EnterpriseSocialDynamicsBaseInfoOutput
     */
    isBusinessExpiration?: boolean;
}
