/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { HrPreferenceDto } from './hr-preference-dto';
import { PositionKeywordDto } from './position-keyword-dto';
/**
 * 
 * @export
 * @interface PositionDisplayForCompus
 */
export interface PositionDisplayForCompus {
    /**
     * 
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    positionId?: number;
    /**
     * 职位guid（缩写）
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    positionGuid?: string;
    /**
     * 企业Guid（缩写）
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    enterpriseGuid?: string;
    /**
     * 急聘：true
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 毕业生：true
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isReceiveGraduate?: boolean;
    /**
     * 工作性质
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    workProperty?: string | null;
    /**
     * 工作性质ID
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    workPropertyID?: number;
    /**
     * 职位名称
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    positionName?: string | null;
    /**
     * 发布日期
     * @type {Date}
     * @memberof PositionDisplayForCompus
     */
    publishTime?: Date;
    /**
     * 工资
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    payPackage?: string | null;
    /**
     * 工作地
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    workPlace?: string | null;
    /**
     * 招聘人数
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    positionAmount?: string | null;
    /**
     * 工龄
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    workAge?: string | null;
    /**
     * 学历
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    degreeName?: string | null;
    /**
     * 企业id
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    enterpriseID?: number;
    /**
     * 企业名称
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    enterpriseName?: string | null;
    /**
     * 职位描述
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    positionDescription?: string | null;
    /**
     * 企业规模
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 福利
     * @type {Array<string>}
     * @memberof PositionDisplayForCompus
     */
    positionWelfareNames?: Array<string> | null;
    /**
     * 企业行业
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    enterpriseIndustry?: string | null;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 企业是否有形象展示
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    haveImageDisplay?: boolean;
    /**
     * 企业性质
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    enterpriseProperty?: string | null;
    /**
     * 企业性质
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    enterprisePropertyId?: number;
    /**
     * 企业Logo
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 百度坐标纬度
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    baiduMapLat?: number | null;
    /**
     * 百度地图经度
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    baiduMapLon?: number | null;
    /**
     * 详细地址
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    workAddress?: string | null;
    /**
     * 是否收藏
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isFavorite?: boolean;
    /**
     * 收藏ID
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    favoriteID?: number;
    /**
     * 是否投递
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isDeliver?: boolean;
    /**
     * 已投递文案
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    deliverText?: string | null;
    /**
     * 投递ID
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    deliverID?: number;
    /**
     * 企业云信ID
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    yxEnterpriseID?: string | null;
    /**
     * 求职者云信ID
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    yxJobSeekerID?: string | null;
    /**
     * 聊天按钮状态，求职者未登=0，企业未开通业务=1，聊一聊=2，继续聊=3。ChatStatus !=1 说明企业开有直聊业务，可以进行直聊
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    chatStatus?: number;
    /**
     * 只有聊一聊和继续聊两种状态，具体操作需要客户端根据状态码进行
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    chatStatusString?: string | null;
    /**
     * ChatStatus = 1 的时候 提示 企业尚未开通直聊业务或业务状态异常，请联系管理员 这个信息，不给往下操作 ChatStatus = 2 的时候 提示 请登录求职者账号
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    chatErrMsg?: string | null;
    /**
     * 外语
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    languageName?: string | null;
    /**
     * 外语水平
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    languageLevelName?: string | null;
    /**
     * 专业要求
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    educationSpecialtyName?: string | null;
    /**
     * 职称要求
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    requirementOfWorkTitleName?: string | null;
    /**
     * 年龄说明文本
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    ageRangeMessage?: string | null;
    /**
     * 更新时间
     * @type {Date}
     * @memberof PositionDisplayForCompus
     */
    updateTime?: Date | null;
    /**
     * 回复标签
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    replyTags?: string | null;
    /**
     * 职位关键词
     * @type {Array<PositionKeywordDto>}
     * @memberof PositionDisplayForCompus
     */
    positionKeywords?: Array<PositionKeywordDto> | null;
    /**
     * 企业社交活跃度标签数组
     * @type {Array<string>}
     * @memberof PositionDisplayForCompus
     */
    activationTags?: Array<string> | null;
    /**
     * 企业是否在线
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    online?: boolean;
    /**
     * 企业发布的社区动态数量
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    dynamicsNum?: number;
    /**
     * 支付宝jobid
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    aliJobId?: string | null;
    /**
     * 是否是代招
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isAgentRecruit?: boolean | null;
    /**
     * 代招公司名称
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    agentEnterpriseName?: string | null;
    /**
     * 
     * @type {HrPreferenceDto}
     * @memberof PositionDisplayForCompus
     */
    hrPreference?: HrPreferenceDto;
    /**
     * 是否使用默认简历投递
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isUseDefaultResume?: boolean;
    /**
     * 是否是邀请聊天(闪招)
     * @type {boolean}
     * @memberof PositionDisplayForCompus
     */
    isInvitationChat?: boolean;
    /**
     * 邀请聊(闪招)天开始时间
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    invitationChatStartTime?: string | null;
    /**
     * 邀请聊(闪招)天结束时间
     * @type {string}
     * @memberof PositionDisplayForCompus
     */
    invitationChatEndTime?: string | null;
    /**
     * 点击量
     * @type {number}
     * @memberof PositionDisplayForCompus
     */
    compusHis?: number;
}
