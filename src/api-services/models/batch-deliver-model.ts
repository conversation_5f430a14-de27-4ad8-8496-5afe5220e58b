/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ApplicationPlatform } from './application-platform';
import { EnumClickFrom } from './enum-click-from';
import { ResumeDeliveryTypeEnum } from './resume-delivery-type-enum';
/**
 * 简历批量投递实体
 * @export
 * @interface BatchDeliverModel
 */
export interface BatchDeliverModel {
    /**
     * 支付宝小程序的authCode
     * @type {string}
     * @memberof BatchDeliverModel
     */
    authCode?: string | null;
    /**
     * 职位Guid字符串，用逗号(\",\")隔开
     * @type {string}
     * @memberof BatchDeliverModel
     */
    positionGuids?: string | null;
    /**
     * 简历Guid
     * @type {string}
     * @memberof BatchDeliverModel
     */
    resumeGuid?: string;
    /**
     * 
     * @type {ApplicationPlatform}
     * @memberof BatchDeliverModel
     */
    from?: ApplicationPlatform;
    /**
     * 直播guid
     * @type {string}
     * @memberof BatchDeliverModel
     */
    liveGuid?: string | null;
    /**
     * 
     * @type {ResumeDeliveryTypeEnum}
     * @memberof BatchDeliverModel
     */
    resumeDeliveryType?: ResumeDeliveryTypeEnum;
    /**
     * 
     * @type {EnumClickFrom}
     * @memberof BatchDeliverModel
     */
    platform?: EnumClickFrom;
}
