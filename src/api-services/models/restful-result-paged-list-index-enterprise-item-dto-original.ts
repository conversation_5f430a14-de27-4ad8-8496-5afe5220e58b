/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PagedListIndexEnterpriseItemDtoOriginal } from './paged-list-index-enterprise-item-dto-original';
/**
 * 
 * @export
 * @interface RestfulResultPagedListIndexEnterpriseItemDtoOriginal
 */
export interface RestfulResultPagedListIndexEnterpriseItemDtoOriginal {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultPagedListIndexEnterpriseItemDtoOriginal
     */
    code?: number | null;
    /**
     * 
     * @type {PagedListIndexEnterpriseItemDtoOriginal}
     * @memberof RestfulResultPagedListIndexEnterpriseItemDtoOriginal
     */
    data?: PagedListIndexEnterpriseItemDtoOriginal;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultPagedListIndexEnterpriseItemDtoOriginal
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultPagedListIndexEnterpriseItemDtoOriginal
     */
    now?: Date;
}
