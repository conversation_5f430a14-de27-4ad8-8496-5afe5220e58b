/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingEnterpriseListItem } from './meeting-enterprise-list-item';
/**
 * 
 * @export
 * @interface PagedListMeetingEnterpriseListItem
 */
export interface PagedListMeetingEnterpriseListItem {
    /**
     * 
     * @type {number}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<MeetingEnterpriseListItem>}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    items?: Array<MeetingEnterpriseListItem> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListMeetingEnterpriseListItem
     */
    hasNextPages?: boolean;
}
