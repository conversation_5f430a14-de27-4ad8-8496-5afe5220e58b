/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { LoadingADModelDto } from './loading-admodel-dto';
/**
 * 
 * @export
 * @interface LoadingADDto
 */
export interface LoadingADDto {
    /**
     * 1.广告类型：1代表单层返回广告，2代表2层返回广告。
     * @type {number}
     * @memberof LoadingADDto
     */
    adTypeId?: number;
    /**
     * 
     * @type {string}
     * @memberof LoadingADDto
     */
    name?: string | null;
    /**
     * 
     * @type {Array<LoadingADModelDto>}
     * @memberof LoadingADDto
     */
    ad?: Array<LoadingADModelDto> | null;
}
