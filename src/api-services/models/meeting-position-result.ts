/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 企业职位
 * @export
 * @interface MeetingPositionResult
 */
export interface MeetingPositionResult {
    /**
     * 企业id
     * @type {number}
     * @memberof MeetingPositionResult
     */
    enterpriseID?: number;
    /**
     * 职位id
     * @type {number}
     * @memberof MeetingPositionResult
     */
    positionID?: number;
    /**
     * 职位名称
     * @type {string}
     * @memberof MeetingPositionResult
     */
    positionName?: string | null;
    /**
     * 企业名称
     * @type {string}
     * @memberof MeetingPositionResult
     */
    enterpriseName?: string | null;
    /**
     * 职位排序
     * @type {number}
     * @memberof MeetingPositionResult
     */
    positionSort?: number;
    /**
     * 招聘人数
     * @type {number}
     * @memberof MeetingPositionResult
     */
    positionAmount?: number;
    /**
     * 学历要求
     * @type {string}
     * @memberof MeetingPositionResult
     */
    requirementOfEducationDegree?: string | null;
    /**
     * 性别要求
     * @type {string}
     * @memberof MeetingPositionResult
     */
    requirementOfSex?: string | null;
    /**
     * 工龄要求
     * @type {number}
     * @memberof MeetingPositionResult
     */
    requirementOfWorkAge?: number;
    /**
     * 薪资待遇
     * @type {string}
     * @memberof MeetingPositionResult
     */
    payPackage?: string | null;
    /**
     * 工作地
     * @type {string}
     * @memberof MeetingPositionResult
     */
    workPlace?: string | null;
    /**
     * 职位级别
     * @type {string}
     * @memberof MeetingPositionResult
     */
    positionLevel?: string | null;
    /**
     * 职称要求
     * @type {string}
     * @memberof MeetingPositionResult
     */
    requirementOfWorkTitle?: string | null;
    /**
     * 是否是毕业生职位
     * @type {boolean}
     * @memberof MeetingPositionResult
     */
    isReceiveGraduate?: boolean;
    /**
     * 职位描述
     * @type {string}
     * @memberof MeetingPositionResult
     */
    positionDescription?: string | null;
    /**
     * 职位是否匹配关键词
     * @type {boolean}
     * @memberof MeetingPositionResult
     */
    isMatch?: boolean;
}
