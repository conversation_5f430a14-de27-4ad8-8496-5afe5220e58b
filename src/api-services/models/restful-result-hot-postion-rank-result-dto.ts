/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { HotPostionRankResultDto } from './hot-postion-rank-result-dto';
/**
 * 
 * @export
 * @interface RestfulResultHotPostionRankResultDto
 */
export interface RestfulResultHotPostionRankResultDto {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultHotPostionRankResultDto
     */
    code?: number | null;
    /**
     * 
     * @type {HotPostionRankResultDto}
     * @memberof RestfulResultHotPostionRankResultDto
     */
    data?: HotPostionRankResultDto;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultHotPostionRankResultDto
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultHotPostionRankResultDto
     */
    now?: Date;
}
