/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { BlueFloorSecond } from './blue-floor-second';
/**
 * 蓝领职位：第一级
 * @export
 * @interface BlueFloorFirst
 */
export interface BlueFloorFirst {
    /**
     * 
     * @type {number}
     * @memberof BlueFloorFirst
     */
    id?: number;
    /**
     * 
     * @type {string}
     * @memberof BlueFloorFirst
     */
    name?: string | null;
    /**
     * 
     * @type {string}
     * @memberof BlueFloorFirst
     */
    category?: string | null;
    /**
     * 
     * @type {string}
     * @memberof BlueFloorFirst
     */
    icon?: string | null;
    /**
     * 
     * @type {number}
     * @memberof BlueFloorFirst
     */
    sort?: number;
    /**
     * 
     * @type {string}
     * @memberof BlueFloorFirst
     */
    imgSrc?: string | null;
    /**
     * 
     * @type {Array<BlueFloorSecond>}
     * @memberof BlueFloorFirst
     */
    secondList?: Array<BlueFloorSecond> | null;
}
