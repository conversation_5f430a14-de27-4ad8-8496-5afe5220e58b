/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 20230523 logo文章推送开发 推送文章列表
 * @export
 * @interface IndexArticlePushItemDto
 */
export interface IndexArticlePushItemDto {
    /**
     * ArticlePosition的主键ID
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    articlePositionID?: number;
    /**
     * 文章id
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    articleId?: number;
    /**
     * 文章关联LogoID
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    logoID?: number;
    /**
     * 企业Gid
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    enterpriseId?: number;
    /**
     * 企业Guid
     * @type {string}
     * @memberof IndexArticlePushItemDto
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof IndexArticlePushItemDto
     */
    enterpriseName?: string | null;
    /**
     * 职位名称
     * @type {string}
     * @memberof IndexArticlePushItemDto
     */
    positionName?: string | null;
    /**
     * 创建时间
     * @type {Date}
     * @memberof IndexArticlePushItemDto
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof IndexArticlePushItemDto
     */
    updateTime?: Date;
    /**
     * 排序
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    sort?: number;
    /**
     * 地市区分
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    districtId?: number;
    /**
     * 点击量
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    clickCount?: number;
    /**
     * 曝光量
     * @type {number}
     * @memberof IndexArticlePushItemDto
     */
    exposureCount?: number;
    /**
     * 企业logo
     * @type {string}
     * @memberof IndexArticlePushItemDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 跳转链接
     * @type {string}
     * @memberof IndexArticlePushItemDto
     */
    linkUrl?: string | null;
}
