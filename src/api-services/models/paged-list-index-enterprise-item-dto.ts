/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexEnterpriseItemDto } from './index-enterprise-item-dto';
/**
 * 
 * @export
 * @interface PagedListIndexEnterpriseItemDto
 */
export interface PagedListIndexEnterpriseItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<IndexEnterpriseItemDto>}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    items?: Array<IndexEnterpriseItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexEnterpriseItemDto
     */
    hasNextPages?: boolean;
}
