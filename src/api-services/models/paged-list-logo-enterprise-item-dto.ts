/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { LogoEnterpriseItemDto } from './logo-enterprise-item-dto';
/**
 * 
 * @export
 * @interface PagedListLogoEnterpriseItemDto
 */
export interface PagedListLogoEnterpriseItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<LogoEnterpriseItemDto>}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    items?: Array<LogoEnterpriseItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListLogoEnterpriseItemDto
     */
    hasNextPages?: boolean;
}
