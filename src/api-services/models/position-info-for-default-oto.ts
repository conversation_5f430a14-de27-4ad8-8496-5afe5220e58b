/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PartTimePayMode } from './part-time-pay-mode';
import { PayUnit } from './pay-unit';
/**
 * OTO首页用的基类
 * @export
 * @interface PositionInfoForDefaultOTO
 */
export interface PositionInfoForDefaultOTO {
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    positionId?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    positionGuid?: string;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    positionName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    enterpriseId?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    enterPriseGuid?: string;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    enterpriseName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    requirementOfWorkAge?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    requirementOfWorkAgeName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    requirementOfEducationDegree?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    requirementOfEducationDegreeName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    payPackage?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    payPackageName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    positionAmount?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    positionAmountName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    educationSpecialty1?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    requirementOfEducationSpecialtyName?: string | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    workDistrict1?: number;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    workCity?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    address?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof PositionInfoForDefaultOTO
     */
    isReceiveGraduate?: boolean;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    workProperty?: number;
    /**
     * 
     * @type {Date}
     * @memberof PositionInfoForDefaultOTO
     */
    publishTime?: Date;
    /**
     * 
     * @type {string}
     * @memberof PositionInfoForDefaultOTO
     */
    positionWelfareIDs?: string | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PositionInfoForDefaultOTO
     */
    positionWelfares?: Array<string> | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    payPackageFrom?: number;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    payPackageTo?: number;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    payMonth?: number | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    monthPayPackageFrom?: number | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    monthPayPackageTo?: number | null;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    partTimeSettlementMode?: number | null;
    /**
     * 
     * @type {PartTimePayMode}
     * @memberof PositionInfoForDefaultOTO
     */
    partTimePayMode?: PartTimePayMode;
    /**
     * 
     * @type {PayUnit}
     * @memberof PositionInfoForDefaultOTO
     */
    payUnit?: PayUnit;
    /**
     * 
     * @type {number}
     * @memberof PositionInfoForDefaultOTO
     */
    workCycle?: number | null;
}
