/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * <br />&nbsp; 新闻 = 1<br />&nbsp; 招聘会 = 2<br />&nbsp; 常规招聘会 = 3<br />&nbsp; 行业专场招聘会 = 4<br />&nbsp; 四季大型招聘会 = 5<br />&nbsp; 毕业生专场招聘会 = 6<br />&nbsp; 校园招聘会 = 7<br />&nbsp; 中高级人才招聘会 = 8<br />&nbsp; 网络招聘会 = 9<br />&nbsp; 就业指导滚动视频 = 10<br />&nbsp; 就业指导精彩视频 = 11<br />&nbsp; 就业指导专家坐堂 = 12<br />&nbsp; 全区人才交流大会 = 13<br />&nbsp; 独家专场招聘会 = 14<br />&nbsp; 网络现场招聘会 = 15<br />&nbsp; 公益招聘会 = 16<br />&nbsp; 直播带岗 = 17<br />
 * @export
 * @enum {string}
 */
export enum CategoryType {
    NUMBER_1 = 1,
    NUMBER_2 = 2,
    NUMBER_3 = 3,
    NUMBER_4 = 4,
    NUMBER_5 = 5,
    NUMBER_6 = 6,
    NUMBER_7 = 7,
    NUMBER_8 = 8,
    NUMBER_9 = 9,
    NUMBER_10 = 10,
    NUMBER_11 = 11,
    NUMBER_12 = 12,
    NUMBER_13 = 13,
    NUMBER_14 = 14,
    NUMBER_15 = 15,
    NUMBER_16 = 16,
    NUMBER_17 = 17
}

