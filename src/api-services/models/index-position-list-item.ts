/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionKeywordDto } from './position-keyword-dto';
import { RedDot } from './red-dot';
/**
 * 职位搜索结果列表项实体
 * @export
 * @interface IndexPositionListItem
 */
export interface IndexPositionListItem {
    /**
     * 跟踪guid
     * @type {string}
     * @memberof IndexPositionListItem
     */
    trackingGuid?: string | null;
    /**
     * 状态标签  2、回复时间显示 X分钟前回复：30分钟以内有回复的显示此状态 3、回复次数显示   今日回复X次：显示今日企业发出的消息总数，3次及以上才显示，10以内显示具体数字，10≤ X＜20显示10+次。 4、回复率显示 回复率高：今日企业发出的消息总数≥20次时，显示为回复率高。
     * @type {Array<string>}
     * @memberof IndexPositionListItem
     */
    activationTags?: Array<string> | null;
    /**
     * 
     * @type {number}
     * @memberof IndexPositionListItem
     */
    positionID?: number;
    /**
     * 
     * @type {string}
     * @memberof IndexPositionListItem
     */
    positionGuid?: string;
    /**
     * 
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterpriseGuid?: string;
    /**
     * 
     * @type {number}
     * @memberof IndexPositionListItem
     */
    enterpriseID?: number;
    /**
     * 
     * @type {string}
     * @memberof IndexPositionListItem
     */
    positionName?: string | null;
    /**
     * 企业名称
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterpriseName?: string | null;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 薪资待遇
     * @type {string}
     * @memberof IndexPositionListItem
     */
    payPackage?: string | null;
    /**
     * 薪资最少
     * @type {number}
     * @memberof IndexPositionListItem
     */
    payPackageFrom?: number;
    /**
     * 薪资最大
     * @type {number}
     * @memberof IndexPositionListItem
     */
    payPackageTo?: number;
    /**
     * 工作地点
     * @type {string}
     * @memberof IndexPositionListItem
     */
    workPlace?: string | null;
    /**
     * 发布日期
     * @type {Date}
     * @memberof IndexPositionListItem
     */
    publishTime?: Date;
    /**
     * 发布日期描述 今天、昨天、04-22（本年度的不用显示年份）、2021-12-31（非本年度显示年份）
     * @type {string}
     * @memberof IndexPositionListItem
     */
    publishTimeDescribe?: string | null;
    /**
     * 职位描述
     * @type {string}
     * @memberof IndexPositionListItem
     */
    description?: string | null;
    /**
     * 招聘人数
     * @type {string}
     * @memberof IndexPositionListItem
     */
    positionAmount?: string | null;
    /**
     * 学历
     * @type {string}
     * @memberof IndexPositionListItem
     */
    degreeName?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterpriseProperty?: string | null;
    /**
     * 工龄
     * @type {string}
     * @memberof IndexPositionListItem
     */
    workAge?: string | null;
    /**
     * 是否紧急招聘
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 是否毕业生职位
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    isReceiveGraduate?: boolean;
    /**
     * 工作性质
     * @type {string}
     * @memberof IndexPositionListItem
     */
    workProperty?: string | null;
    /**
     * 工作性质
     * @type {number}
     * @memberof IndexPositionListItem
     */
    workPropertyID?: number;
    /**
     * 
     * @type {number}
     * @memberof IndexPositionListItem
     */
    enterpriseDistrictID?: number;
    /**
     * 单位规模
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 工作福利
     * @type {Array<string>}
     * @memberof IndexPositionListItem
     */
    positionWelfareNames?: Array<string> | null;
    /**
     * 分数
     * @type {number}
     * @memberof IndexPositionListItem
     */
    score?: number;
    /**
     * 是否在线
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    online?: boolean;
    /**
     * 回复率
     * @type {number}
     * @memberof IndexPositionListItem
     */
    replyRatio?: number | null;
    /**
     * 投递率
     * @type {number}
     * @memberof IndexPositionListItem
     */
    deliveryRatio?: number | null;
    /**
     * 值聊
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    zhiliao?: boolean;
    /**
     * 距离
     * @type {string}
     * @memberof IndexPositionListItem
     */
    distance?: string | null;
    /**
     * 单位人数
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterpriseEmployeeNumberName?: string | null;
    /**
     * 企业行业
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterpriseIndustryName?: string | null;
    /**
     * 企业性质
     * @type {string}
     * @memberof IndexPositionListItem
     */
    enterprisePropertyName?: string | null;
    /**
     * Logo
     * @type {string}
     * @memberof IndexPositionListItem
     */
    logoUrl?: string | null;
    /**
     * 关键词
     * @type {Array<PositionKeywordDto>}
     * @memberof IndexPositionListItem
     */
    positionKeywords?: Array<PositionKeywordDto> | null;
    /**
     * 是否已 投递/申请
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    isDeliver?: boolean;
    /**
     * 已投递文案
     * @type {string}
     * @memberof IndexPositionListItem
     */
    deliverText?: string | null;
    /**
     * 类型：0:原获取记录 1:文章广告推送 20230523
     * @type {number}
     * @memberof IndexPositionListItem
     */
    recordType?: number;
    /**
     * 如果是要显示一张图，这里记录图片链接
     * @type {string}
     * @memberof IndexPositionListItem
     */
    recordImageUrl?: string | null;
    /**
     * RecordType1:文章广告推送的时候跳转链接 20230523
     * @type {string}
     * @memberof IndexPositionListItem
     */
    jumpLinkUrl?: string | null;
    /**
     * ArticlePosition的主键ID
     * @type {number}
     * @memberof IndexPositionListItem
     */
    articlePositionID?: number;
    /**
     * 文章关联LogoID
     * @type {number}
     * @memberof IndexPositionListItem
     */
    logoID?: number;
    /**
     * 曝光业务表的主键 [Voodoo_Enterprise].[dbo].[InvitationChatRecord]
     * @type {number}
     * @memberof IndexPositionListItem
     */
    inviChatRecordId?: number;
    /**
     * 曝光量
     * @type {number}
     * @memberof IndexPositionListItem
     */
    exposureCount?: number;
    /**
     * 曝光点击量
     * @type {number}
     * @memberof IndexPositionListItem
     */
    exposureClickCount?: number;
    /**
     * 是否曝光中
     * @type {number}
     * @memberof IndexPositionListItem
     */
    isExposing?: number;
    /**
     * 曝光开始时间
     * @type {Date}
     * @memberof IndexPositionListItem
     */
    exposureBeginTime?: Date;
    /**
     * 曝光结束时间
     * @type {Date}
     * @memberof IndexPositionListItem
     */
    exposureEndTime?: Date;
    /**
     * 
     * @type {number}
     * @memberof IndexPositionListItem
     */
    isSocialPosition?: number;
    /**
     * 工龄要求
     * @type {number}
     * @memberof IndexPositionListItem
     */
    requirementOfWorkAge?: number;
    /**
     * 企业最后回复消息时间描述
     * @type {string}
     * @memberof IndexPositionListItem
     */
    lastReplyMessageTimeDesc?: string | null;
    /**
     * 今天之内企业回复消息的次数
     * @type {number}
     * @memberof IndexPositionListItem
     */
    todayReplyMessageTimes?: number;
    /**
     * 20231120 当前接口的版本信息
     * @type {string}
     * @memberof IndexPositionListItem
     */
    theVersionInfo?: string | null;
    /**
     * 非高亮
     * @type {string}
     * @memberof IndexPositionListItem
     */
    notHitPName?: string | null;
    /**
     * 非高亮
     * @type {string}
     * @memberof IndexPositionListItem
     */
    notHitDescription?: string | null;
    /**
     * 搜素id
     * @type {string}
     * @memberof IndexPositionListItem
     */
    searchId?: string | null;
    /**
     * 所属页码
     * @type {number}
     * @memberof IndexPositionListItem
     */
    page?: number;
    /**
     * 描述的html格式
     * @type {string}
     * @memberof IndexPositionListItem
     */
    descriptionHtml?: string | null;
    /**
     * 是否收藏职位
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    isCollection?: boolean;
    /**
     * 是否是代理招聘
     * @type {boolean}
     * @memberof IndexPositionListItem
     */
    isAgentRecruit?: boolean;
    /**
     * 代理招聘公司名称
     * @type {string}
     * @memberof IndexPositionListItem
     */
    agentEnterpriseName?: string | null;
    /**
     * 
     * @type {RedDot}
     * @memberof IndexPositionListItem
     */
    redDot?: RedDot;
}
