/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionBaseInfoModel } from './position-base-info-model';
/**
 * 
 * @export
 * @interface TopicSearchEnterpriseOutput
 */
export interface TopicSearchEnterpriseOutput {
    /**
     * 企业ID
     * @type {number}
     * @memberof TopicSearchEnterpriseOutput
     */
    enterpriseId?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof TopicSearchEnterpriseOutput
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof TopicSearchEnterpriseOutput
     */
    enterpriseName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof TopicSearchEnterpriseOutput
     */
    logoUrl?: string | null;
    /**
     * 专题项ID
     * @type {number}
     * @memberof TopicSearchEnterpriseOutput
     */
    topicItemId?: number;
    /**
     * 
     * @type {Array<PositionBaseInfoModel>}
     * @memberof TopicSearchEnterpriseOutput
     */
    positions?: Array<PositionBaseInfoModel> | null;
    /**
     * 
     * @type {number}
     * @memberof TopicSearchEnterpriseOutput
     */
    positionCount?: number;
}
