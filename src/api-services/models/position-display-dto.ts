/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { HrPreferenceDto } from './hr-preference-dto';
import { PositionKeywordDto } from './position-keyword-dto';
/**
 * 职位展示Dto
 * @export
 * @interface PositionDisplayDto
 */
export interface PositionDisplayDto {
    /**
     * 
     * @type {number}
     * @memberof PositionDisplayDto
     */
    positionId?: number;
    /**
     * 职位guid（缩写）
     * @type {string}
     * @memberof PositionDisplayDto
     */
    positionGuid?: string;
    /**
     * 企业Guid（缩写）
     * @type {string}
     * @memberof PositionDisplayDto
     */
    enterpriseGuid?: string;
    /**
     * 急聘：true
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 毕业生：true
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isReceiveGraduate?: boolean;
    /**
     * 工作性质
     * @type {string}
     * @memberof PositionDisplayDto
     */
    workProperty?: string | null;
    /**
     * 工作性质ID
     * @type {number}
     * @memberof PositionDisplayDto
     */
    workPropertyID?: number;
    /**
     * 职位名称
     * @type {string}
     * @memberof PositionDisplayDto
     */
    positionName?: string | null;
    /**
     * 发布日期
     * @type {Date}
     * @memberof PositionDisplayDto
     */
    publishTime?: Date;
    /**
     * 工资
     * @type {string}
     * @memberof PositionDisplayDto
     */
    payPackage?: string | null;
    /**
     * 工作地
     * @type {string}
     * @memberof PositionDisplayDto
     */
    workPlace?: string | null;
    /**
     * 招聘人数
     * @type {string}
     * @memberof PositionDisplayDto
     */
    positionAmount?: string | null;
    /**
     * 工龄
     * @type {string}
     * @memberof PositionDisplayDto
     */
    workAge?: string | null;
    /**
     * 学历
     * @type {string}
     * @memberof PositionDisplayDto
     */
    degreeName?: string | null;
    /**
     * 企业id
     * @type {number}
     * @memberof PositionDisplayDto
     */
    enterpriseID?: number;
    /**
     * 企业名称
     * @type {string}
     * @memberof PositionDisplayDto
     */
    enterpriseName?: string | null;
    /**
     * 职位描述
     * @type {string}
     * @memberof PositionDisplayDto
     */
    positionDescription?: string | null;
    /**
     * 企业规模
     * @type {string}
     * @memberof PositionDisplayDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 福利
     * @type {Array<string>}
     * @memberof PositionDisplayDto
     */
    positionWelfareNames?: Array<string> | null;
    /**
     * 企业行业
     * @type {string}
     * @memberof PositionDisplayDto
     */
    enterpriseIndustry?: string | null;
    /**
     * 企业是否属于产业园
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isBelongIndustrialPark?: boolean;
    /**
     * 企业是否有形象展示
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    haveImageDisplay?: boolean;
    /**
     * 企业性质
     * @type {string}
     * @memberof PositionDisplayDto
     */
    enterpriseProperty?: string | null;
    /**
     * 企业性质
     * @type {number}
     * @memberof PositionDisplayDto
     */
    enterprisePropertyId?: number;
    /**
     * 企业Logo
     * @type {string}
     * @memberof PositionDisplayDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 百度坐标纬度
     * @type {number}
     * @memberof PositionDisplayDto
     */
    baiduMapLat?: number | null;
    /**
     * 百度地图经度
     * @type {number}
     * @memberof PositionDisplayDto
     */
    baiduMapLon?: number | null;
    /**
     * 详细地址
     * @type {string}
     * @memberof PositionDisplayDto
     */
    workAddress?: string | null;
    /**
     * 是否收藏
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isFavorite?: boolean;
    /**
     * 收藏ID
     * @type {number}
     * @memberof PositionDisplayDto
     */
    favoriteID?: number;
    /**
     * 是否投递
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isDeliver?: boolean;
    /**
     * 已投递文案
     * @type {string}
     * @memberof PositionDisplayDto
     */
    deliverText?: string | null;
    /**
     * 投递ID
     * @type {number}
     * @memberof PositionDisplayDto
     */
    deliverID?: number;
    /**
     * 企业云信ID
     * @type {string}
     * @memberof PositionDisplayDto
     */
    yxEnterpriseID?: string | null;
    /**
     * 求职者云信ID
     * @type {string}
     * @memberof PositionDisplayDto
     */
    yxJobSeekerID?: string | null;
    /**
     * 聊天按钮状态，求职者未登=0，企业未开通业务=1，聊一聊=2，继续聊=3。ChatStatus !=1 说明企业开有直聊业务，可以进行直聊
     * @type {number}
     * @memberof PositionDisplayDto
     */
    chatStatus?: number;
    /**
     * 只有聊一聊和继续聊两种状态，具体操作需要客户端根据状态码进行
     * @type {string}
     * @memberof PositionDisplayDto
     */
    chatStatusString?: string | null;
    /**
     * ChatStatus = 1 的时候 提示 企业尚未开通直聊业务或业务状态异常，请联系管理员 这个信息，不给往下操作 ChatStatus = 2 的时候 提示 请登录求职者账号
     * @type {string}
     * @memberof PositionDisplayDto
     */
    chatErrMsg?: string | null;
    /**
     * 外语
     * @type {string}
     * @memberof PositionDisplayDto
     */
    languageName?: string | null;
    /**
     * 外语水平
     * @type {string}
     * @memberof PositionDisplayDto
     */
    languageLevelName?: string | null;
    /**
     * 专业要求
     * @type {string}
     * @memberof PositionDisplayDto
     */
    educationSpecialtyName?: string | null;
    /**
     * 职称要求
     * @type {string}
     * @memberof PositionDisplayDto
     */
    requirementOfWorkTitleName?: string | null;
    /**
     * 年龄说明文本
     * @type {string}
     * @memberof PositionDisplayDto
     */
    ageRangeMessage?: string | null;
    /**
     * 更新时间
     * @type {Date}
     * @memberof PositionDisplayDto
     */
    updateTime?: Date | null;
    /**
     * 回复标签
     * @type {string}
     * @memberof PositionDisplayDto
     */
    replyTags?: string | null;
    /**
     * 职位关键词
     * @type {Array<PositionKeywordDto>}
     * @memberof PositionDisplayDto
     */
    positionKeywords?: Array<PositionKeywordDto> | null;
    /**
     * 企业社交活跃度标签数组
     * @type {Array<string>}
     * @memberof PositionDisplayDto
     */
    activationTags?: Array<string> | null;
    /**
     * 企业是否在线
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    online?: boolean;
    /**
     * 企业发布的社区动态数量
     * @type {number}
     * @memberof PositionDisplayDto
     */
    dynamicsNum?: number;
    /**
     * 支付宝jobid
     * @type {string}
     * @memberof PositionDisplayDto
     */
    aliJobId?: string | null;
    /**
     * 是否是代招
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isAgentRecruit?: boolean | null;
    /**
     * 代招公司名称
     * @type {string}
     * @memberof PositionDisplayDto
     */
    agentEnterpriseName?: string | null;
    /**
     * 
     * @type {HrPreferenceDto}
     * @memberof PositionDisplayDto
     */
    hrPreference?: HrPreferenceDto;
    /**
     * 是否使用默认简历投递
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isUseDefaultResume?: boolean;
    /**
     * 是否是邀请聊天(闪招)
     * @type {boolean}
     * @memberof PositionDisplayDto
     */
    isInvitationChat?: boolean;
    /**
     * 邀请聊(闪招)天开始时间
     * @type {string}
     * @memberof PositionDisplayDto
     */
    invitationChatStartTime?: string | null;
    /**
     * 邀请聊(闪招)天结束时间
     * @type {string}
     * @memberof PositionDisplayDto
     */
    invitationChatEndTime?: string | null;
}
