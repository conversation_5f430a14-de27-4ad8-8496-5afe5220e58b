/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SydwListItemDto } from './sydw-list-item-dto';
/**
 * 
 * @export
 * @interface PagedListSydwListItemDto
 */
export interface PagedListSydwListItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListSydwListItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSydwListItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSydwListItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSydwListItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<SydwListItemDto>}
     * @memberof PagedListSydwListItemDto
     */
    items?: Array<SydwListItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSydwListItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSydwListItemDto
     */
    hasNextPages?: boolean;
}
