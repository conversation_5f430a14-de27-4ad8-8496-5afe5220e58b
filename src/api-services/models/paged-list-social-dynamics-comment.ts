/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SocialDynamicsComment } from './social-dynamics-comment';
/**
 * 
 * @export
 * @interface PagedListSocialDynamicsComment
 */
export interface PagedListSocialDynamicsComment {
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComment
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComment
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComment
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComment
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<SocialDynamicsComment>}
     * @memberof PagedListSocialDynamicsComment
     */
    items?: Array<SocialDynamicsComment> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSocialDynamicsComment
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSocialDynamicsComment
     */
    hasNextPages?: boolean;
}
