/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface RankSqlEntity
 */
export interface RankSqlEntity {
    /**
     * 
     * @type {number}
     * @memberof RankSqlEntity
     */
    rank?: number;
    /**
     * 
     * @type {string}
     * @memberof RankSqlEntity
     */
    rankPositionName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof RankSqlEntity
     */
    rankPositionProportion?: string | null;
    /**
     * 
     * @type {number}
     * @memberof RankSqlEntity
     */
    rankPositionRankChange?: number | null;
}
