/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { QccOriginalNameModel } from './qcc-original-name-model';
/**
 * 企业工商详情
 * @export
 * @interface QccECIV4Model
 */
export interface QccECIV4Model {
    /**
     * 内部KeyNo
     * @type {string}
     * @memberof QccECIV4Model
     */
    keyNo?: string | null;
    /**
     * 公司名称
     * @type {string}
     * @memberof QccECIV4Model
     */
    name?: string | null;
    /**
     * 注册号
     * @type {string}
     * @memberof QccECIV4Model
     */
    no?: string | null;
    /**
     * 登记机关
     * @type {string}
     * @memberof QccECIV4Model
     */
    belongOrg?: string | null;
    /**
     * 法人名
     * @type {string}
     * @memberof QccECIV4Model
     */
    operName?: string | null;
    /**
     * 成立日期
     * @type {string}
     * @memberof QccECIV4Model
     */
    startDate?: string | null;
    /**
     * 吊销日期
     * @type {string}
     * @memberof QccECIV4Model
     */
    endDate?: string | null;
    /**
     * 企业状态
     * @type {string}
     * @memberof QccECIV4Model
     */
    status?: string | null;
    /**
     * 省份
     * @type {string}
     * @memberof QccECIV4Model
     */
    province?: string | null;
    /**
     * 更新日期
     * @type {string}
     * @memberof QccECIV4Model
     */
    updatedDate?: string | null;
    /**
     * 社会统一信用代码
     * @type {string}
     * @memberof QccECIV4Model
     */
    creditCode?: string | null;
    /**
     * 注册资本
     * @type {string}
     * @memberof QccECIV4Model
     */
    registCapi?: string | null;
    /**
     * 企业类型
     * @type {string}
     * @memberof QccECIV4Model
     */
    econKind?: string | null;
    /**
     * 地址
     * @type {string}
     * @memberof QccECIV4Model
     */
    address?: string | null;
    /**
     * 经营范围
     * @type {string}
     * @memberof QccECIV4Model
     */
    scope?: string | null;
    /**
     * 营业开始日期
     * @type {string}
     * @memberof QccECIV4Model
     */
    termStart?: string | null;
    /**
     * 营业结束日期
     * @type {string}
     * @memberof QccECIV4Model
     */
    teamEnd?: string | null;
    /**
     * 发照日期
     * @type {string}
     * @memberof QccECIV4Model
     */
    checkDate?: string | null;
    /**
     * 组织机构代码
     * @type {string}
     * @memberof QccECIV4Model
     */
    orgNo?: string | null;
    /**
     * 是否IPO上市(0为未上市，1为上市)
     * @type {string}
     * @memberof QccECIV4Model
     */
    isOnStock?: string | null;
    /**
     * 上市公司代码
     * @type {string}
     * @memberof QccECIV4Model
     */
    stockNumber?: string | null;
    /**
     * 上市类型
     * @type {string}
     * @memberof QccECIV4Model
     */
    stockType?: string | null;
    /**
     * 曾用名
     * @type {Array<QccOriginalNameModel>}
     * @memberof QccECIV4Model
     */
    originalName?: Array<QccOriginalNameModel> | null;
    /**
     * 
     * @type {string}
     * @memberof QccECIV4Model
     */
    imageUrl?: string | null;
    /**
     * 企业类型，0-公司，1-社会组织 ，3-香港公司，4-事业单位，6-基金会，7-医院，8-海外公司，9-律师事务所，10-学校 ，-1-其他
     * @type {string}
     * @memberof QccECIV4Model
     */
    entType?: string | null;
    /**
     * 实缴资本
     * @type {string}
     * @memberof QccECIV4Model
     */
    recCap?: string | null;
    /**
     * 行业
     * @type {string}
     * @memberof QccECIV4Model
     */
    industry?: string | null;
    /**
     * 区域
     * @type {string}
     * @memberof QccECIV4Model
     */
    district?: string | null;
}
