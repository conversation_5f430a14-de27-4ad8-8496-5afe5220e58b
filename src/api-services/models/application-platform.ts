/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * <br />&nbsp; PC = 0<br />&nbsp; Wechat = 1<br />&nbsp; Android = 2<br />&nbsp; IOS = 3<br />&nbsp; H5 = 4<br />&nbsp; TikTok = 6<br />&nbsp; Alipay = 7<br />&nbsp; PCP2P = 10<br />&nbsp; WechatP2P = 11<br />&nbsp; AndroidP2P = 12<br />&nbsp; IOSP2P = 13<br />&nbsp; H5P2P = 14<br />
 * @export
 * @enum {string}
 */
export enum ApplicationPlatform {
    PC = 'PC',
    Wechat = 'Wechat',
    Android = 'Android',
    IOS = 'IOS',
    H5 = 'H5',
    TikTok = 'TikTok',
    Alipay = 'Alipay',
    PCP2P = 'PCP2P',
    WechatP2P = 'WechatP2P',
    AndroidP2P = 'AndroidP2P',
    IOSP2P = 'IOSP2P',
    H5P2P = 'H5P2P'
}

