/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DiscoverOption } from './discover-option';
/**
 * 
 * @export
 * @interface ValueTaskDiscoverOption
 */
export interface ValueTaskDiscoverOption {
    /**
     * 
     * @type {boolean}
     * @memberof ValueTaskDiscoverOption
     */
    isCompleted?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ValueTaskDiscoverOption
     */
    isCompletedSuccessfully?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ValueTaskDiscoverOption
     */
    isFaulted?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof ValueTaskDiscoverOption
     */
    isCanceled?: boolean;
    /**
     * 
     * @type {DiscoverOption}
     * @memberof ValueTaskDiscoverOption
     */
    result?: DiscoverOption;
}
