/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EnumClickFrom } from './enum-click-from';
/**
 * 
 * @export
 * @interface MeetingPositionDeliverParameter
 */
export interface MeetingPositionDeliverParameter {
    /**
     * 
     * @type {number}
     * @memberof MeetingPositionDeliverParameter
     */
    enterpriseId?: number;
    /**
     * 
     * @type {number}
     * @memberof MeetingPositionDeliverParameter
     */
    positionId?: number;
    /**
     * 
     * @type {number}
     * @memberof MeetingPositionDeliverParameter
     */
    meetingId?: number;
    /**
     * 
     * @type {number}
     * @memberof MeetingPositionDeliverParameter
     */
    resumeId?: number;
    /**
     * 
     * @type {EnumClickFrom}
     * @memberof MeetingPositionDeliverParameter
     */
    platform?: EnumClickFrom;
}
