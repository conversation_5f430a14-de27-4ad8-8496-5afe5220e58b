/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { RankSqlEntity } from './rank-sql-entity';
/**
 * 热门职位竞争排行榜
 * @export
 * @interface HotPostionRankResultDto
 */
export interface HotPostionRankResultDto {
    /**
     * 热门职位
     * @type {Array<RankSqlEntity>}
     * @memberof HotPostionRankResultDto
     */
    hotPositins?: Array<RankSqlEntity> | null;
    /**
     * 我也不懂这个是啥，抄来的
     * @type {Array<RankSqlEntity>}
     * @memberof HotPostionRankResultDto
     */
    competeList?: Array<RankSqlEntity> | null;
}
