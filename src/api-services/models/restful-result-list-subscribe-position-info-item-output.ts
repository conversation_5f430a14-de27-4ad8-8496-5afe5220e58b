/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SubscribePositionInfoItemOutput } from './subscribe-position-info-item-output';
/**
 * 
 * @export
 * @interface RestfulResultListSubscribePositionInfoItemOutput
 */
export interface RestfulResultListSubscribePositionInfoItemOutput {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultListSubscribePositionInfoItemOutput
     */
    code?: number | null;
    /**
     * 
     * @type {Array<SubscribePositionInfoItemOutput>}
     * @memberof RestfulResultListSubscribePositionInfoItemOutput
     */
    data?: Array<SubscribePositionInfoItemOutput> | null;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultListSubscribePositionInfoItemOutput
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultListSubscribePositionInfoItemOutput
     */
    now?: Date;
}
