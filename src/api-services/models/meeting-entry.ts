/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface MeetingEntry
 */
export interface MeetingEntry {
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    url?: string | null;
    /**
     * 
     * @type {number}
     * @memberof MeetingEntry
     */
    id?: number;
    /**
     * 
     * @type {number}
     * @memberof MeetingEntry
     */
    meetingId?: number;
    /**
     * 
     * @type {number}
     * @memberof MeetingEntry
     */
    meetingType?: number;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    theme?: string | null;
    /**
     * 
     * @type {number}
     * @memberof MeetingEntry
     */
    meetingOpenId?: number;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    title?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    description?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    startTime?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    meetingPlace?: string | null;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    cover?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingEntry
     */
    registrationPositionEnable?: boolean;
    /**
     * 招聘会置顶
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isTop?: boolean;
    /**
     * 招聘会标签
     * @type {string}
     * @memberof MeetingEntry
     */
    tags?: string | null;
    /**
     * 是否报名
     * @type {boolean}
     * @memberof MeetingEntry
     */
    apply?: boolean;
    /**
     * 是否是O2O
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isO2O?: boolean;
    /**
     * 是否使用电子票　0:使用　1:不使用
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isUseETicket?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isEnterCheck?: boolean;
    /**
     * 
     * @type {number}
     * @memberof MeetingEntry
     */
    enterCheckType?: number;
    /**
     * 用于前台判断请求类型
     * @type {number}
     * @memberof MeetingEntry
     */
    requestType?: number;
    /**
     * 招聘会地势
     * @type {number}
     * @memberof MeetingEntry
     */
    districtID?: number;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isEnter?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isApply?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingEntry
     */
    deliverable?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof MeetingEntry
     */
    isFqw?: boolean;
    /**
     * 
     * @type {string}
     * @memberof MeetingEntry
     */
    tips?: string | null;
    /**
     * 投递文本
     * @type {string}
     * @memberof MeetingEntry
     */
    deliverText?: string | null;
}
