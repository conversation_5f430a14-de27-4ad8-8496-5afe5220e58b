/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionListModel } from './position-list-model';
/**
 * 企业搜索返回原始字段
 * @export
 * @interface IndexEnterpriseItemDtoOriginal
 */
export interface IndexEnterpriseItemDtoOriginal {
    /**
     * 企业ID
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseID?: number;
    /**
     * 企业Guid
     * @type {string}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseName?: string | null;
    /**
     * 单位性质
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseProperty?: number;
    /**
     * 雇佣人数
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseEmployeeNumber?: number;
    /**
     * 行业
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseIndustry?: number;
    /**
     * 联系地址
     * @type {string}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseAddress?: string | null;
    /**
     * 网联归属
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseDistrictID?: number;
    /**
     * logo
     * @type {string}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    materialImpUrl?: string | null;
    /**
     * 是否公开地址
     * @type {boolean}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    isAddressPublic?: boolean;
    /**
     * 操作
     * @type {string}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    operation?: string | null;
    /**
     * 坐标系
     * @type {Array<string>}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    geoLocation?: Array<string> | null;
    /**
     * 工作地
     * @type {Array<number>}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    workDistrict?: Array<number> | null;
    /**
     * 行业
     * @type {Array<number>}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    positionIndustry?: Array<number> | null;
    /**
     * 职能
     * @type {Array<number>}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    positionCareer?: Array<number> | null;
    /**
     * 最近刷新时间
     * @type {Date}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    lastRefreshTime?: Date;
    /**
     * 是否可用
     * @type {boolean}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseEnableFlag?: boolean;
    /**
     * 
     * @type {PositionListModel}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    positionList?: PositionListModel;
    /**
     * 职位数量
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    positionCount?: number;
    /**
     * 职位数量
     * @type {number}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    posiCountSearchCtrl?: number;
    /**
     * 企业Logo
     * @type {string}
     * @memberof IndexEnterpriseItemDtoOriginal
     */
    enterpriseLogoUrl?: string | null;
}
