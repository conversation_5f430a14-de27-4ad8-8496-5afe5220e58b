/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingTag } from './meeting-tag';
/**
 * 招聘会列表项
 * @export
 * @interface MeetingListItemDto
 */
export interface MeetingListItemDto {
    /**
     * 招聘会标题
     * @type {string}
     * @memberof MeetingListItemDto
     */
    title?: string | null;
    /**
     * 招聘会文章ID  o2o的招聘会id
     * @type {number}
     * @memberof MeetingListItemDto
     */
    articleID?: number;
    /**
     * 招聘会地址
     * @type {string}
     * @memberof MeetingListItemDto
     */
    address?: string | null;
    /**
     * 招聘会开始日期
     * @type {Date}
     * @memberof MeetingListItemDto
     */
    startTime?: Date;
    /**
     * 开始日期
     * @type {string}
     * @memberof MeetingListItemDto
     */
    startTimeChars?: string | null;
    /**
     * 短的结束日期
     * @type {string}
     * @memberof MeetingListItemDto
     */
    shortEndTimeChars?: string | null;
    /**
     * 是否校园招聘会
     * @type {boolean}
     * @memberof MeetingListItemDto
     */
    isO2O?: boolean;
    /**
     * 校园招聘会id 20220812
     * @type {number}
     * @memberof MeetingListItemDto
     */
    schoolMeetingID?: number;
    /**
     * 是否现场招聘会
     * @type {boolean}
     * @memberof MeetingListItemDto
     */
    isLive?: boolean;
    /**
     * 是否网络招聘会
     * @type {boolean}
     * @memberof MeetingListItemDto
     */
    isNetWork?: boolean;
    /**
     * 是否行业招聘会
     * @type {boolean}
     * @memberof MeetingListItemDto
     */
    isIndustry?: boolean;
    /**
     * 招聘会地址
     * @type {string}
     * @memberof MeetingListItemDto
     */
    articleUrl?: string | null;
    /**
     * 招聘会标签
     * @type {Array<MeetingTag>}
     * @memberof MeetingListItemDto
     */
    tags?: Array<MeetingTag> | null;
    /**
     * 开始时间
     * @type {string}
     * @memberof MeetingListItemDto
     */
    articlePublishTimePart?: string | null;
}
