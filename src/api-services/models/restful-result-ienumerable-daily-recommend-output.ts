/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DailyRecommendOutput } from './daily-recommend-output';
/**
 * 
 * @export
 * @interface RestfulResultIEnumerableDailyRecommendOutput
 */
export interface RestfulResultIEnumerableDailyRecommendOutput {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultIEnumerableDailyRecommendOutput
     */
    code?: number | null;
    /**
     * 
     * @type {Array<DailyRecommendOutput>}
     * @memberof RestfulResultIEnumerableDailyRecommendOutput
     */
    data?: Array<DailyRecommendOutput> | null;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultIEnumerableDailyRecommendOutput
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultIEnumerableDailyRecommendOutput
     */
    now?: Date;
}
