/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { BlueFloorFirst } from './blue-floor-first';
/**
 * 
 * @export
 * @interface RestfulResultListBlueFloorFirst
 */
export interface RestfulResultListBlueFloorFirst {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultListBlueFloorFirst
     */
    code?: number | null;
    /**
     * 
     * @type {Array<BlueFloorFirst>}
     * @memberof RestfulResultListBlueFloorFirst
     */
    data?: Array<BlueFloorFirst> | null;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultListBlueFloorFirst
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultListBlueFloorFirst
     */
    now?: Date;
}
