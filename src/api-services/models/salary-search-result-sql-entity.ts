/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { EntSalaryModel } from './ent-salary-model';
import { JobSalaryModel } from './job-salary-model';
import { JobSeekerAvgSalaryWithNumbersSqlEntity } from './job-seeker-avg-salary-with-numbers-sql-entity';
import { JobSeekerAvgSalaryWithPositionCountSqlEntity } from './job-seeker-avg-salary-with-position-count-sql-entity';
/**
 * 薪酬报告
 * @export
 * @interface SalarySearchResultSqlEntity
 */
export interface SalarySearchResultSqlEntity {
    /**
     * 企业薪资
     * @type {Array<EntSalaryModel>}
     * @memberof SalarySearchResultSqlEntity
     */
    enterpriseSalary?: Array<EntSalaryModel> | null;
    /**
     * 岗位薪资
     * @type {Array<JobSalaryModel>}
     * @memberof SalarySearchResultSqlEntity
     */
    jobSalary?: Array<JobSalaryModel> | null;
    /**
     * 地市
     * @type {number}
     * @memberof SalarySearchResultSqlEntity
     */
    workPlace?: number;
    /**
     * 地市
     * @type {string}
     * @memberof SalarySearchResultSqlEntity
     */
    workPlaceName?: string | null;
    /**
     * 职能名称
     * @type {string}
     * @memberof SalarySearchResultSqlEntity
     */
    positionTypeName?: string | null;
    /**
     * 
     * @type {JobSeekerAvgSalaryWithNumbersSqlEntity}
     * @memberof SalarySearchResultSqlEntity
     */
    jobSeekerCount?: JobSeekerAvgSalaryWithNumbersSqlEntity;
    /**
     * 
     * @type {JobSeekerAvgSalaryWithPositionCountSqlEntity}
     * @memberof SalarySearchResultSqlEntity
     */
    deliverPositionCount?: JobSeekerAvgSalaryWithPositionCountSqlEntity;
    /**
     * 
     * @type {string}
     * @memberof SalarySearchResultSqlEntity
     */
    keyContent?: string | null;
}
