/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PagedListIndexPositionListItem } from './paged-list-index-position-list-item';
/**
 * 
 * @export
 * @interface RestfulResultPagedListIndexPositionListItem
 */
export interface RestfulResultPagedListIndexPositionListItem {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultPagedListIndexPositionListItem
     */
    code?: number | null;
    /**
     * 
     * @type {PagedListIndexPositionListItem}
     * @memberof RestfulResultPagedListIndexPositionListItem
     */
    data?: PagedListIndexPositionListItem;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultPagedListIndexPositionListItem
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultPagedListIndexPositionListItem
     */
    now?: Date;
}
