/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionListItem } from './position-list-item';
/**
 * 列表项
 * @export
 * @interface LogoEnterpriseListItemDto
 */
export interface LogoEnterpriseListItemDto {
    /**
     * 企业ID
     * @type {number}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseID?: number;
    /**
     * LogoID
     * @type {number}
     * @memberof LogoEnterpriseListItemDto
     */
    logoID?: number;
    /**
     * 企业guid
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseName?: string | null;
    /**
     * logo
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 有些企业是点击到文章
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    linkUrl?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseProperty?: string | null;
    /**
     * 单位性质ID
     * @type {number}
     * @memberof LogoEnterpriseListItemDto
     */
    enterprisePropertyID?: number;
    /**
     * 企业行业
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseIndustry?: string | null;
    /**
     * 雇佣人数
     * @type {string}
     * @memberof LogoEnterpriseListItemDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 职位数
     * @type {number}
     * @memberof LogoEnterpriseListItemDto
     */
    positionCount?: number;
    /**
     * 职位列表
     * @type {Array<PositionListItem>}
     * @memberof LogoEnterpriseListItemDto
     */
    positions?: Array<PositionListItem> | null;
}
