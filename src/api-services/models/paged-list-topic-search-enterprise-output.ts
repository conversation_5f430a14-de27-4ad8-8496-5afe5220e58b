/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { TopicSearchEnterpriseOutput } from './topic-search-enterprise-output';
/**
 * 
 * @export
 * @interface PagedListTopicSearchEnterpriseOutput
 */
export interface PagedListTopicSearchEnterpriseOutput {
    /**
     * 
     * @type {number}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<TopicSearchEnterpriseOutput>}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    items?: Array<TopicSearchEnterpriseOutput> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListTopicSearchEnterpriseOutput
     */
    hasNextPages?: boolean;
}
