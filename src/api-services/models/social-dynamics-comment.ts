/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SocialDynamicsComment } from './social-dynamics-comment';
/**
 * 社交动态信息表
 * @export
 * @interface SocialDynamicsComment
 */
export interface SocialDynamicsComment {
    /**
     * 主键
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    commentId?: number;
    /**
     * 唯一标识
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    parentCommentId?: number;
    /**
     * 动态所属ID或者资讯guid
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    hostGuid?: string;
    /**
     * 动态所属求职者ID
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    commentatorId?: number;
    /**
     * 动态回应评论
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    toCommentId?: number | null;
    /**
     * 动态回应者的企业id
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    commentatorEnterpriseId?: number | null;
    /**
     * 职位
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    positionName?: string | null;
    /**
     * 内容
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    content?: string | null;
    /**
     * 评论点赞数量
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    likes?: number;
    /**
     * 审核时间
     * @type {Date}
     * @memberof SocialDynamicsComment
     */
    auditTime?: Date | null;
    /**
     * 审核状态
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    auditState?: number;
    /**
     * 创建时间
     * @type {Date}
     * @memberof SocialDynamicsComment
     */
    createTime?: Date;
    /**
     * 是否被删除
     * @type {boolean}
     * @memberof SocialDynamicsComment
     */
    isDeleted?: boolean;
    /**
     * ip
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    ip?: string | null;
    /**
     * 评论 层级
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    level?: number;
    /**
     * 地点归属地
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    ipAdress?: string | null;
    /**
     * 0动态， 1资讯
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    commentType?: number;
    /**
     * 
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    time?: string | null;
    /**
     * 被评论者名
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    toCommentatorName?: string | null;
    /**
     * 评论者名字
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    commentatorName?: string | null;
    /**
     * 被评论者头像
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    toCommentatorAvatar?: string | null;
    /**
     * 评论者头像
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    commentatorAvatar?: string | null;
    /**
     * 评论者头像
     * @type {Array<SocialDynamicsComment>}
     * @memberof SocialDynamicsComment
     */
    jobList?: Array<SocialDynamicsComment> | null;
    /**
     * 其他评论数
     * @type {number}
     * @memberof SocialDynamicsComment
     */
    otherCount?: number;
    /**
     * 评论者信息
     * @type {string}
     * @memberof SocialDynamicsComment
     */
    commentatorInfo?: string | null;
    /**
     * 是否作者
     * @type {boolean}
     * @memberof SocialDynamicsComment
     */
    isAuthor?: boolean;
    /**
     * 是否作者
     * @type {boolean}
     * @memberof SocialDynamicsComment
     */
    toCommentatorIsAuthor?: boolean;
    /**
     * 是否点赞了
     * @type {boolean}
     * @memberof SocialDynamicsComment
     */
    isLike?: boolean;
}
