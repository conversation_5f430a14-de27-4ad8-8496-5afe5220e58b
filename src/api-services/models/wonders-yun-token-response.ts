/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 柳州龙城云oauth2/token 接口响应数据模型
 * @export
 * @interface WondersYunTokenResponse
 */
export interface WondersYunTokenResponse {
    /**
     * 用于调用 access_token，接口获取授权后的 access token
     * @type {string}
     * @memberof WondersYunTokenResponse
     */
    accessToken?: string | null;
    /**
     * token 的类型
     * @type {string}
     * @memberof WondersYunTokenResponse
     */
    tokenType?: string | null;
    /**
     * access_token 的生命周期，单位是秒数
     * @type {number}
     * @memberof WondersYunTokenResponse
     */
    expiresIn?: number;
    /**
     * 当前获取的可操作类型，目前只提供 read
     * @type {string}
     * @memberof WondersYunTokenResponse
     */
    scope?: string | null;
    /**
     * 当前授权用户的用户名
     * @type {string}
     * @memberof WondersYunTokenResponse
     */
    user?: string | null;
    /**
     * 响应代码
     * @type {number}
     * @memberof WondersYunTokenResponse
     */
    code?: number;
    /**
     * 响应消息
     * @type {string}
     * @memberof WondersYunTokenResponse
     */
    message?: string | null;
}
