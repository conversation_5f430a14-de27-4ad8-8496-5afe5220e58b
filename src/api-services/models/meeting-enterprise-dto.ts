/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingPositionListItem } from './meeting-position-list-item';
/**
 * 招聘会 企业详情
 * @export
 * @interface MeetingEnterpriseDto
 */
export interface MeetingEnterpriseDto {
    /**
     * 企业ID
     * @type {number}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseID?: number;
    /**
     * 企业名称 高亮
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseName?: string | null;
    /**
     * 企业名称 不高亮
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseNameOriginal?: string | null;
    /**
     * 是否展示展位号
     * @type {boolean}
     * @memberof MeetingEnterpriseDto
     */
    isShowBoothsNo?: boolean;
    /**
     * 场馆数
     * @type {number}
     * @memberof MeetingEnterpriseDto
     */
    attendMeetingPlaceCount?: number;
    /**
     * 展位号
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    seatNum?: string | null;
    /**
     * 展位号
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    oldSeatNum?: string | null;
    /**
     * 单位性质
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseProperty?: string | null;
    /**
     * 单位性质ID
     * @type {number}
     * @memberof MeetingEnterpriseDto
     */
    enterprisePropertyId?: number;
    /**
     * 单位行业
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseIndustry?: string | null;
    /**
     * 单位规模
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseEmployeeNumber?: string | null;
    /**
     * 单位说明
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseIntroduction?: string | null;
    /**
     * 企业Logo
     * @type {string}
     * @memberof MeetingEnterpriseDto
     */
    enterpriseLogoUrl?: string | null;
    /**
     * 职位列表
     * @type {Array<MeetingPositionListItem>}
     * @memberof MeetingEnterpriseDto
     */
    positions?: Array<MeetingPositionListItem> | null;
    /**
     * 职位数量
     * @type {number}
     * @memberof MeetingEnterpriseDto
     */
    positionCount?: number;
}
