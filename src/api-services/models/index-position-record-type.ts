/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * <br />&nbsp; Nomal = 0<br />&nbsp; ArticlePositionPush = 1<br />&nbsp; ArticlePositionPushEnte = 2<br />&nbsp; ArticlePositionPushPosi = 3<br />&nbsp; ImagePushLinkUrl = 4<br />&nbsp; ImagePushEnte = 5<br />&nbsp; ImagePushPosi = 6<br />&nbsp; ExpoPosi = 7<br />
 * @export
 * @enum {string}
 */
export enum IndexPositionRecordType {
    Nomal = 'Nomal',
    ArticlePositionPush = 'ArticlePositionPush',
    ArticlePositionPushEnte = 'ArticlePositionPushEnte',
    ArticlePositionPushPosi = 'ArticlePositionPushPosi',
    ImagePushLinkUrl = 'ImagePushLinkUrl',
    ImagePushEnte = 'ImagePushEnte',
    ImagePushPosi = 'ImagePushPosi',
    ExpoPosi = 'ExpoPosi'
}

