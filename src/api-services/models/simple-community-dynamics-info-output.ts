/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { CommunityAuditState } from './community-audit-state';
import { CommunityDynamicsImageInfo } from './community-dynamics-image-info';
import { CommunityDynamicsType } from './community-dynamics-type';
import { CommunityPositionBaseInfo } from './community-position-base-info';
import { CommunityQuestion } from './community-question';
/**
 * 发现社区动态简化输出数据模型
 * @export
 * @interface SimpleCommunityDynamicsInfoOutput
 */
export interface SimpleCommunityDynamicsInfoOutput {
    /**
     * 唯一标识
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    dynamicsInfoGuid?: string;
    /**
     * 动态发送者
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    sender?: string | null;
    /**
     * 动态发送者的GUID(企业的或者求职者的)
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    senderGuid?: string;
    /**
     * 动态作者头像URL
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    senderAvatarUrl?: string | null;
    /**
     * 发送时间
     * @type {Date}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    sendTime?: Date;
    /**
     * 发送时间中文显示
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    chnSendTime?: string | null;
    /**
     * 标题
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    title?: string | null;
    /**
     * 外链
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    outerLink?: string | null;
    /**
     * 点赞数量
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    likes?: number;
    /**
     * 
     * @type {CommunityDynamicsType}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    type?: CommunityDynamicsType;
    /**
     * 图片清单
     * @type {Array<CommunityDynamicsImageInfo>}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    images?: Array<CommunityDynamicsImageInfo> | null;
    /**
     * 视频播放地址
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    videoUrl?: string | null;
    /**
     * 视频播放时长（单位：秒）
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    duration?: number;
    /**
     * 视频截图URL
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    snapshotUrl?: string | null;
    /**
     * 视频封面宽度
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    snapshotWidth?: number;
    /**
     * 视频封面高度
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    snapshotHeight?: number;
    /**
     * 当前求职者是否点赞此动态
     * @type {boolean}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    liked?: boolean;
    /**
     * 当前求职者是否收藏此动态
     * @type {boolean}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    collected?: boolean;
    /**
     * 是否置顶
     * @type {boolean}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    isTop?: boolean | null;
    /**
     * 置顶顺序
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    topOrder?: number | null;
    /**
     * 默认0 为企业发布，1为运营，2为资讯的发布
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    fromTypeId?: number | null;
    /**
     * 企业招聘业务是否过期
     * @type {boolean}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    isBusinessExpiration?: boolean;
    /**
     * 
     * @type {CommunityAuditState}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    auditState?: CommunityAuditState;
    /**
     * 审核备注
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    nopassMsg?: string | null;
    /**
     * 动态浏览量
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    browseCount?: string | null;
    /**
     * 
     * @type {CommunityPositionBaseInfo}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    positionBaseInfo?: CommunityPositionBaseInfo;
    /**
     * 动态职位名称
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    dynamicsPosName?: string | null;
    /**
     * 首页滑屏token
     * @type {string}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    rollToken?: string | null;
    /**
     * 排序 20231206
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    orderIndex?: number;
    /**
     * 1:问答数据
     * @type {number}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    itemDataType?: number;
    /**
     * 问答列表
     * @type {Array<CommunityQuestion>}
     * @memberof SimpleCommunityDynamicsInfoOutput
     */
    questionList?: Array<CommunityQuestion> | null;
}
