/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 投递的简历
 * @export
 * @interface DeliverResumeItem
 */
export interface DeliverResumeItem {
    /**
     * 简历ID
     * @type {number}
     * @memberof DeliverResumeItem
     */
    resumeID?: number;
    /**
     * 简历Guid
     * @type {string}
     * @memberof DeliverResumeItem
     */
    resumeGuid?: string;
    /**
     * 简历名称
     * @type {string}
     * @memberof DeliverResumeItem
     */
    resumeName?: string | null;
    /**
     * 简历状态
     * @type {string}
     * @memberof DeliverResumeItem
     */
    resumeStateName?: string | null;
    /**
     * 简历状态
     * @type {number}
     * @memberof DeliverResumeItem
     */
    resumeState?: number | null;
    /**
     * 默认=True
     * @type {boolean}
     * @memberof DeliverResumeItem
     */
    isDefault?: boolean;
    /**
     * 是否投递
     * @type {boolean}
     * @memberof DeliverResumeItem
     */
    isDelivery?: boolean;
    /**
     * 
     * @type {number}
     * @memberof DeliverResumeItem
     */
    totalScore?: number;
    /**
     * 
     * @type {string}
     * @memberof DeliverResumeItem
     */
    subItemScores?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof DeliverResumeItem
     */
    isFromSenior?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof DeliverResumeItem
     */
    isAttachment?: boolean;
    /**
     * 完整度
     * @type {number}
     * @memberof DeliverResumeItem
     */
    totalScoreNew?: number;
    /**
     * 
     * @type {string}
     * @memberof DeliverResumeItem
     */
    subItemScoresNew?: string | null;
    /**
     * 最后修改时间
     * @type {Date}
     * @memberof DeliverResumeItem
     */
    lastEditTime?: Date | null;
}
