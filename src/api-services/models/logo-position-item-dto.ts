/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PartTimePayMode } from './part-time-pay-mode';
import { PayUnit } from './pay-unit';
/**
 * logo的职位实体
 * @export
 * @interface LogoPositionItemDto
 */
export interface LogoPositionItemDto {
    /**
     * 企业id
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    enterpriseID?: number;
    /**
     * 职位id
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    positionID?: number;
    /**
     * 职位guid
     * @type {string}
     * @memberof LogoPositionItemDto
     */
    positionGuid?: string;
    /**
     * 职位名称
     * @type {string}
     * @memberof LogoPositionItemDto
     */
    positionName?: string | null;
    /**
     * 薪资
     * @type {string}
     * @memberof LogoPositionItemDto
     */
    payPackage?: string | null;
    /**
     * 广告排序
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    advSort?: number | null;
    /**
     * 招聘人数
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    positionAmount?: number;
    /**
     * 招聘人数
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    workProperty?: number;
    /**
     * 薪酬区间范围最小值
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    payPackageFrom?: number;
    /**
     * 薪酬区间范围最大值
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    payPackageTo?: number;
    /**
     * 薪酬月份
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    payMonth?: number | null;
    /**
     * 薪酬计算最小值
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    monthPayPackageFrom?: number | null;
    /**
     * 薪酬计算最大值
     * @type {number}
     * @memberof LogoPositionItemDto
     */
    monthPayPackageTo?: number | null;
    /**
     * 
     * @type {PartTimePayMode}
     * @memberof LogoPositionItemDto
     */
    partTimePayMode?: PartTimePayMode;
    /**
     * 
     * @type {PayUnit}
     * @memberof LogoPositionItemDto
     */
    payUnit?: PayUnit;
}
