/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { LogoEnterpriseListItemDto } from './logo-enterprise-list-item-dto';
/**
 * 
 * @export
 * @interface PagedListLogoEnterpriseListItemDto
 */
export interface PagedListLogoEnterpriseListItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<LogoEnterpriseListItemDto>}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    items?: Array<LogoEnterpriseListItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListLogoEnterpriseListItemDto
     */
    hasNextPages?: boolean;
}
