/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PositionKeywordDto } from './position-keyword-dto';
/**
 * 职位基本信息
 * @export
 * @interface CommunityPositionBaseInfo
 */
export interface CommunityPositionBaseInfo {
    /**
     * 职位ID
     * @type {number}
     * @memberof CommunityPositionBaseInfo
     */
    positionId?: number;
    /**
     * 职位GUID
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    positionGuid?: string;
    /**
     * 职位名称
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    name?: string | null;
    /**
     * 薪资范围
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    salaryRange?: string | null;
    /**
     * 工作地点
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    location?: string | null;
    /**
     * 工作经验要求
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    requirementOfWorkAge?: string | null;
    /**
     * 学历要求
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    requirementOfEducationDegree?: string | null;
    /**
     * 是否急聘
     * @type {boolean}
     * @memberof CommunityPositionBaseInfo
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 是否是毕业生职位
     * @type {boolean}
     * @memberof CommunityPositionBaseInfo
     */
    isReceiveGraduate?: boolean;
    /**
     * 工作性质代码(全职,兼职,钟点工,临时,实习)
     * @type {number}
     * @memberof CommunityPositionBaseInfo
     */
    workProperty?: number;
    /**
     * 工作性质
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    workPropertyName?: string | null;
    /**
     * 招聘总人数
     * @type {number}
     * @memberof CommunityPositionBaseInfo
     */
    positionAmount?: number;
    /**
     * 职位描述
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    positionDescription?: string | null;
    /**
     * 所属行业
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    positionCareer?: string | null;
    /**
     * 职位状态
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    positionState?: string | null;
    /**
     * 是否已 投递/申请
     * @type {boolean}
     * @memberof CommunityPositionBaseInfo
     */
    isDeliver?: boolean;
    /**
     * 已投递文案
     * @type {string}
     * @memberof CommunityPositionBaseInfo
     */
    deliverText?: string | null;
    /**
     * 关键词
     * @type {Array<PositionKeywordDto>}
     * @memberof CommunityPositionBaseInfo
     */
    positionKeywords?: Array<PositionKeywordDto> | null;
}
