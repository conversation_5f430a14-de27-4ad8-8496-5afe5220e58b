/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PagedListMeetingEnterpriseListItem } from './paged-list-meeting-enterprise-list-item';
/**
 * 
 * @export
 * @interface RestfulResultPagedListMeetingEnterpriseListItem
 */
export interface RestfulResultPagedListMeetingEnterpriseListItem {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultPagedListMeetingEnterpriseListItem
     */
    code?: number | null;
    /**
     * 
     * @type {PagedListMeetingEnterpriseListItem}
     * @memberof RestfulResultPagedListMeetingEnterpriseListItem
     */
    data?: PagedListMeetingEnterpriseListItem;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultPagedListMeetingEnterpriseListItem
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultPagedListMeetingEnterpriseListItem
     */
    now?: Date;
}
