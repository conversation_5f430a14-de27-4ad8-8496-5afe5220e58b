/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingTag } from './meeting-tag';
/**
 * 
 * @export
 * @interface O2OMeetingListItemDto
 */
export interface O2OMeetingListItemDto {
    /**
     * 招聘会标题
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    title?: string | null;
    /**
     * 招聘会文章ID  o2o的招聘会id
     * @type {number}
     * @memberof O2OMeetingListItemDto
     */
    articleID?: number;
    /**
     * 招聘会地址
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    address?: string | null;
    /**
     * 招聘会开始日期
     * @type {Date}
     * @memberof O2OMeetingListItemDto
     */
    startTime?: Date;
    /**
     * 开始日期
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    startTimeChars?: string | null;
    /**
     * 短的结束日期
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    shortEndTimeChars?: string | null;
    /**
     * 是否校园招聘会
     * @type {boolean}
     * @memberof O2OMeetingListItemDto
     */
    isO2O?: boolean;
    /**
     * 校园招聘会id 20220812
     * @type {number}
     * @memberof O2OMeetingListItemDto
     */
    schoolMeetingID?: number;
    /**
     * 是否现场招聘会
     * @type {boolean}
     * @memberof O2OMeetingListItemDto
     */
    isLive?: boolean;
    /**
     * 是否网络招聘会
     * @type {boolean}
     * @memberof O2OMeetingListItemDto
     */
    isNetWork?: boolean;
    /**
     * 是否行业招聘会
     * @type {boolean}
     * @memberof O2OMeetingListItemDto
     */
    isIndustry?: boolean;
    /**
     * 招聘会地址
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    articleUrl?: string | null;
    /**
     * 招聘会标签
     * @type {Array<MeetingTag>}
     * @memberof O2OMeetingListItemDto
     */
    tags?: Array<MeetingTag> | null;
    /**
     * 开始时间
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    articlePublishTimePart?: string | null;
    /**
     * 企业数量
     * @type {number}
     * @memberof O2OMeetingListItemDto
     */
    enterpriseCount?: number;
    /**
     * 职位数量
     * @type {number}
     * @memberof O2OMeetingListItemDto
     */
    positionCount?: number;
    /**
     * 岗位数
     * @type {number}
     * @memberof O2OMeetingListItemDto
     */
    positionAmount?: number;
    /**
     * 左侧学校logo
     * @type {string}
     * @memberof O2OMeetingListItemDto
     */
    logo?: string | null;
    /**
     * 置顶排序
     * @type {number}
     * @memberof O2OMeetingListItemDto
     */
    topIndex?: number;
}
