/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SocialDynamicsInfoOutput } from './social-dynamics-info-output';
/**
 * 
 * @export
 * @interface PagedListSocialDynamicsInfoOutput
 */
export interface PagedListSocialDynamicsInfoOutput {
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<SocialDynamicsInfoOutput>}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    items?: Array<SocialDynamicsInfoOutput> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSocialDynamicsInfoOutput
     */
    hasNextPages?: boolean;
}
