/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { CommunityAuditState } from './community-audit-state';
import { CommunityDynamicsType } from './community-dynamics-type';
import { DepartmentPositions } from './department-positions';
import { EnterpriseWithPositions } from './enterprise-with-positions';
import { SocialDynamicsImageInfo } from './social-dynamics-image-info';
/**
 * 社交动态输出数据模型
 * @export
 * @interface SocialDynamicsInfoOutput
 */
export interface SocialDynamicsInfoOutput {
    /**
     * 
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    dynamicsInfoId?: number;
    /**
     * 唯一标识
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    dynamicsInfoGuid?: string;
    /**
     * 动态发送者
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    sender?: string | null;
    /**
     * 动态发送者的GUID(企业的或者求职者的)
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    senderGuid?: string;
    /**
     * 动态作者头像URL
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    senderAvatarUrl?: string | null;
    /**
     * 发送时间
     * @type {Date}
     * @memberof SocialDynamicsInfoOutput
     */
    sendTime?: Date;
    /**
     * 发送时间中文显示
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    chnSendTime?: string | null;
    /**
     * 标题
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    title?: string | null;
    /**
     * 文本内容
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    content?: string | null;
    /**
     * 动态发出的客户端来源，保存RegFrom枚举值
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    addFrom?: number;
    /**
     * 包含的话题数组
     * @type {Array<string>}
     * @memberof SocialDynamicsInfoOutput
     */
    topics?: Array<string> | null;
    /**
     * 点赞数量
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    likes?: number;
    /**
     * 收藏数量
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    collections?: number;
    /**
     * 评论数量
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    comments?: number;
    /**
     * 
     * @type {CommunityDynamicsType}
     * @memberof SocialDynamicsInfoOutput
     */
    type?: CommunityDynamicsType;
    /**
     * 图片清单
     * @type {Array<SocialDynamicsImageInfo>}
     * @memberof SocialDynamicsInfoOutput
     */
    images?: Array<SocialDynamicsImageInfo> | null;
    /**
     * 视频播放地址
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    videoUrl?: string | null;
    /**
     * 视频播放时长（单位：秒）
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    duration?: number;
    /**
     * 视频截图URL
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    snapshotUrl?: string | null;
    /**
     * 视频封面宽度
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    snapshotWidth?: number;
    /**
     * 视频封面高度
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    snapshotHeight?: number;
    /**
     * 当前求职者是否点赞此动态
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    liked?: boolean;
    /**
     * 当前求职者是否收藏此动态
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    collected?: boolean;
    /**
     * 当前求职者是否已关注企业
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    followed?: boolean;
    /**
     * 是否置顶
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    isTop?: boolean | null;
    /**
     * 置顶顺序
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    topOrder?: number | null;
    /**
     * 
     * @type {CommunityAuditState}
     * @memberof SocialDynamicsInfoOutput
     */
    auditState?: CommunityAuditState;
    /**
     * 外链
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    outerLink?: string | null;
    /**
     * 动态设定是否展示
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    isShow?: boolean;
    /**
     * 动态定位地点
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    location?: string | null;
    /**
     * 动态位置信息经度
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    zoomX?: number | null;
    /**
     * 动态位置信息纬度
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    zoomY?: number | null;
    /**
     * 动态是否包含多家公司的推荐职位
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    isMultiEnterprisesRecommend?: boolean;
    /**
     * 动态里包含的单公司分部门推荐职位列表
     * @type {Array<DepartmentPositions>}
     * @memberof SocialDynamicsInfoOutput
     */
    recommendPositions?: Array<DepartmentPositions> | null;
    /**
     * 动态里包含的多公司不分部门推荐职位列表
     * @type {Array<EnterpriseWithPositions>}
     * @memberof SocialDynamicsInfoOutput
     */
    multiEnterprisesRecommendPositions?: Array<EnterpriseWithPositions> | null;
    /**
     * 企业所属行业
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    enterpriseIndustry?: string | null;
    /**
     * 企业雇员规模
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    employeeNumber?: string | null;
    /**
     * 资讯类型特有昵称
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    nikeName?: string | null;
    /**
     * 资讯类型特有来源昵称
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    fromName?: string | null;
    /**
     * 是否运营账号
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    isOperater?: boolean;
    /**
     * 默认0 为企业发布，1为运营，2为资讯的发布
     * @type {number}
     * @memberof SocialDynamicsInfoOutput
     */
    fromTypeId?: number | null;
    /**
     * 动态浏览量
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    browseCount?: string | null;
    /**
     * 更新时间
     * @type {Date}
     * @memberof SocialDynamicsInfoOutput
     */
    updatedTime?: Date | null;
    /**
     * 企业招聘业务是否过期
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    isBusinessExpiration?: boolean;
    /**
     * 是否允许评论
     * @type {boolean}
     * @memberof SocialDynamicsInfoOutput
     */
    canComment?: boolean;
    /**
     * 职位动态的职位GUID，客户端用此GUID查询职位的详细信息
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    promotionPositionGuid?: string;
    /**
     * 动态职位名称
     * @type {string}
     * @memberof SocialDynamicsInfoOutput
     */
    dynamicsPosName?: string | null;
}
