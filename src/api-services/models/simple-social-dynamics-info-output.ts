/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { CommunityDynamicsType } from './community-dynamics-type';
import { SocialDynamicsImageInfo } from './social-dynamics-image-info';
/**
 * 社交动态简化输出数据模型
 * @export
 * @interface SimpleSocialDynamicsInfoOutput
 */
export interface SimpleSocialDynamicsInfoOutput {
    /**
     * 灵活ID，保存收藏ID、点赞ID等
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    flexId?: number;
    /**
     * 唯一标识
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    dynamicsInfoGuid?: string;
    /**
     * 动态发送者
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    sender?: string | null;
    /**
     * 动态发送者的GUID(企业的或者求职者的)
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    senderGuid?: string;
    /**
     * 动态作者头像URL
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    senderAvatarUrl?: string | null;
    /**
     * 发送时间
     * @type {Date}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    sendTime?: Date;
    /**
     * 发送时间中文显示
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    chnSendTime?: string | null;
    /**
     * 标题
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    title?: string | null;
    /**
     * 外链
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    outerLink?: string | null;
    /**
     * 点赞数量
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    likes?: number;
    /**
     * 
     * @type {CommunityDynamicsType}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    type?: CommunityDynamicsType;
    /**
     * 图片清单
     * @type {Array<SocialDynamicsImageInfo>}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    images?: Array<SocialDynamicsImageInfo> | null;
    /**
     * 视频播放地址
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    videoUrl?: string | null;
    /**
     * 视频播放时长（单位：秒）
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    duration?: number;
    /**
     * 视频截图URL
     * @type {string}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    snapshotUrl?: string | null;
    /**
     * 视频封面宽度
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    snapshotWidth?: number;
    /**
     * 视频封面高度
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    snapshotHeight?: number;
    /**
     * 当前求职者是否点赞此动态
     * @type {boolean}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    liked?: boolean;
    /**
     * 当前求职者是否收藏此动态
     * @type {boolean}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    collected?: boolean;
    /**
     * 是否置顶
     * @type {boolean}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    isTop?: boolean | null;
    /**
     * 置顶顺序
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    topOrder?: number | null;
    /**
     * 
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    dynamicsInfoId?: number;
    /**
     * 
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    enterpriseId?: number;
    /**
     * 
     * @type {number}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    fromTypeId?: number;
    /**
     * 企业招聘业务是否过期
     * @type {boolean}
     * @memberof SimpleSocialDynamicsInfoOutput
     */
    isBusinessExpiration?: boolean;
}
