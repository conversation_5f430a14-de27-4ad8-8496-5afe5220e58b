/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SchoolBroadcastItemOutput } from './school-broadcast-item-output';
/**
 * 
 * @export
 * @interface PagedListSchoolBroadcastItemOutput
 */
export interface PagedListSchoolBroadcastItemOutput {
    /**
     * 
     * @type {number}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<SchoolBroadcastItemOutput>}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    items?: Array<SchoolBroadcastItemOutput> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSchoolBroadcastItemOutput
     */
    hasNextPages?: boolean;
}
