/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 资讯列表项实体
 * @export
 * @interface ZixunListItemDto
 */
export interface ZixunListItemDto {
    /**
     * 文章ID
     * @type {number}
     * @memberof ZixunListItemDto
     */
    articleID?: number;
    /**
     * 文章guid
     * @type {string}
     * @memberof ZixunListItemDto
     */
    articleGuid?: string;
    /**
     * 文章标题
     * @type {string}
     * @memberof ZixunListItemDto
     */
    articleTitle?: string | null;
    /**
     * 文章链接
     * @type {string}
     * @memberof ZixunListItemDto
     */
    articleUrl?: string | null;
    /**
     * 关键词
     * @type {string}
     * @memberof ZixunListItemDto
     */
    articleKeyWords?: string | null;
    /**
     * 评论数
     * @type {number}
     * @memberof ZixunListItemDto
     */
    commentCount?: number | null;
    /**
     * 文章类型id
     * @type {number}
     * @memberof ZixunListItemDto
     */
    articleCategoryID?: number;
    /**
     * 文章类型显示名称 （栏目名）
     * @type {string}
     * @memberof ZixunListItemDto
     */
    articleCategoryName?: string | null;
}
