/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexEnterpriseItemDtoOriginal } from './index-enterprise-item-dto-original';
/**
 * 
 * @export
 * @interface PagedListIndexEnterpriseItemDtoOriginal
 */
export interface PagedListIndexEnterpriseItemDtoOriginal {
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<IndexEnterpriseItemDtoOriginal>}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    items?: Array<IndexEnterpriseItemDtoOriginal> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexEnterpriseItemDtoOriginal
     */
    hasNextPages?: boolean;
}
