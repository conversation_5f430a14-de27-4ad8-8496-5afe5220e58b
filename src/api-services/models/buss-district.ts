/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * <br />&nbsp; 广西 = 0<br />&nbsp; 桂林 = 1<br />&nbsp; 柳州 = 2<br />&nbsp; 梧州 = 4<br />&nbsp; 桂平 = 5<br />&nbsp; 百色 = 6<br />&nbsp; 钦州 = 7<br />&nbsp; 河池 = 8<br />&nbsp; 北海 = 9<br />&nbsp; 桂兴 = 10<br />&nbsp; 防港 = 11<br />&nbsp; 玉林 = 12<br />&nbsp; 崇左 = 13<br />&nbsp; 贵港 = 14<br />&nbsp; 来宾 = 15<br />&nbsp; 合浦 = 16<br />&nbsp; 永福 = 17<br />&nbsp; 贺州 = 18<br />&nbsp; 南宁 = 19<br />&nbsp; 平南 = 20<br />&nbsp; 毕业生频道 = 10000<br />&nbsp; 桂林毕业生就业服务网 = 10001<br />&nbsp; 柳州毕业生就业服务网 = 10002<br />&nbsp; 梧州毕业生就业服务网 = 10004<br />&nbsp; 桂平毕业生就业服务网 = 10005<br />&nbsp; 百色毕业生就业服务网 = 10006<br />&nbsp; 钦州毕业生就业服务网 = 10007<br />&nbsp; 河池毕业生就业服务网 = 10008<br />&nbsp; 北海毕业生就业服务网 = 10009<br />&nbsp; 防城毕业生就业服务网 = 10011<br />&nbsp; 玉林毕业生就业服务网 = 10012<br />&nbsp; 崇左毕业生就业服务网 = 10013<br />&nbsp; 贵港毕业生就业服务网 = 10014<br />&nbsp; 来宾毕业生就业服务网 = 10015<br />&nbsp; 贺州毕业生就业服务网 = 10018<br />&nbsp; 南宁毕业生就业服务网 = 10019<br />&nbsp; 平南毕业生就业服务网 = 10020<br />&nbsp; 军培 = 10021<br />&nbsp; 所有 = -1<br />
 * @export
 * @enum {string}
 */
export enum BussDistrict {
    NUMBER_0 = 0,
    NUMBER_1 = 1,
    NUMBER_2 = 2,
    NUMBER_4 = 4,
    NUMBER_5 = 5,
    NUMBER_6 = 6,
    NUMBER_7 = 7,
    NUMBER_8 = 8,
    NUMBER_9 = 9,
    NUMBER_10 = 10,
    NUMBER_11 = 11,
    NUMBER_12 = 12,
    NUMBER_13 = 13,
    NUMBER_14 = 14,
    NUMBER_15 = 15,
    NUMBER_16 = 16,
    NUMBER_17 = 17,
    NUMBER_18 = 18,
    NUMBER_19 = 19,
    NUMBER_20 = 20,
    NUMBER_10000 = 10000,
    NUMBER_10001 = 10001,
    NUMBER_10002 = 10002,
    NUMBER_10004 = 10004,
    NUMBER_10005 = 10005,
    NUMBER_10006 = 10006,
    NUMBER_10007 = 10007,
    NUMBER_10008 = 10008,
    NUMBER_10009 = 10009,
    NUMBER_10011 = 10011,
    NUMBER_10012 = 10012,
    NUMBER_10013 = 10013,
    NUMBER_10014 = 10014,
    NUMBER_10015 = 10015,
    NUMBER_10018 = 10018,
    NUMBER_10019 = 10019,
    NUMBER_10020 = 10020,
    NUMBER_10021 = 10021,
    NUMBER_MINUS_1 = -1
}

