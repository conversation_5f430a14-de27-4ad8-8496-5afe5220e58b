/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SocialDynamicsComplaintItemDto } from './social-dynamics-complaint-item-dto';
/**
 * 
 * @export
 * @interface PagedListSocialDynamicsComplaintItemDto
 */
export interface PagedListSocialDynamicsComplaintItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<SocialDynamicsComplaintItemDto>}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    items?: Array<SocialDynamicsComplaintItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSocialDynamicsComplaintItemDto
     */
    hasNextPages?: boolean;
}
