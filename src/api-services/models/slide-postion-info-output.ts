/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { PostionGuidAndPage } from './postion-guid-and-page';
/**
 * 
 * @export
 * @interface SlidePostionInfoOutput
 */
export interface SlidePostionInfoOutput {
    /**
     * 
     * @type {PostionGuidAndPage}
     * @memberof SlidePostionInfoOutput
     */
    prev?: PostionGuidAndPage;
    /**
     * 
     * @type {PostionGuidAndPage}
     * @memberof SlidePostionInfoOutput
     */
    next?: PostionGuidAndPage;
    /**
     * 前面的数据集合
     * @type {Array<PostionGuidAndPage>}
     * @memberof SlidePostionInfoOutput
     */
    prevList?: Array<PostionGuidAndPage> | null;
    /**
     * 后面的数据集合
     * @type {Array<PostionGuidAndPage>}
     * @memberof SlidePostionInfoOutput
     */
    nextList?: Array<PostionGuidAndPage> | null;
}
