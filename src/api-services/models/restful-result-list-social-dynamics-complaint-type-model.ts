/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SocialDynamicsComplaintTypeModel } from './social-dynamics-complaint-type-model';
/**
 * 
 * @export
 * @interface RestfulResultListSocialDynamicsComplaintTypeModel
 */
export interface RestfulResultListSocialDynamicsComplaintTypeModel {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultListSocialDynamicsComplaintTypeModel
     */
    code?: number | null;
    /**
     * 
     * @type {Array<SocialDynamicsComplaintTypeModel>}
     * @memberof RestfulResultListSocialDynamicsComplaintTypeModel
     */
    data?: Array<SocialDynamicsComplaintTypeModel> | null;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultListSocialDynamicsComplaintTypeModel
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultListSocialDynamicsComplaintTypeModel
     */
    now?: Date;
}
