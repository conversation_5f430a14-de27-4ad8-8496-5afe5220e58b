/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 企业点播视频查询结果输出模型
 * @export
 * @interface EnterpriseVideoOutput
 */
export interface EnterpriseVideoOutput {
    /**
     * 视频唯一标识
     * @type {string}
     * @memberof EnterpriseVideoOutput
     */
    videoGuid?: string;
    /**
     * 视频播放时长（单位：秒）
     * @type {number}
     * @memberof EnterpriseVideoOutput
     */
    duration?: number;
    /**
     * 视频标题
     * @type {string}
     * @memberof EnterpriseVideoOutput
     */
    videoTitle?: string | null;
    /**
     * 视频描述
     * @type {string}
     * @memberof EnterpriseVideoOutput
     */
    describe?: string | null;
    /**
     * 原始文件名
     * @type {string}
     * @memberof EnterpriseVideoOutput
     */
    originalFilename?: string | null;
    /**
     * 视频截图URL
     * @type {string}
     * @memberof EnterpriseVideoOutput
     */
    snapshotUrl?: string | null;
    /**
     * 截图宽度
     * @type {number}
     * @memberof EnterpriseVideoOutput
     */
    snapshotWidth?: number;
    /**
     * 截图高度
     * @type {number}
     * @memberof EnterpriseVideoOutput
     */
    snapshotHeight?: number;
    /**
     * 播放数
     * @type {number}
     * @memberof EnterpriseVideoOutput
     */
    reviewTimes?: number;
    /**
     * 点赞数
     * @type {number}
     * @memberof EnterpriseVideoOutput
     */
    likes?: number;
    /**
     * 上传时间
     * @type {Date}
     * @memberof EnterpriseVideoOutput
     */
    createTime?: Date;
    /**
     * 视频播放地址
     * @type {string}
     * @memberof EnterpriseVideoOutput
     */
    origUrl?: string | null;
    /**
     * 动态发出的客户端来源，保存RegFrom枚举值
     * @type {number}
     * @memberof EnterpriseVideoOutput
     */
    addFrom?: number;
}
