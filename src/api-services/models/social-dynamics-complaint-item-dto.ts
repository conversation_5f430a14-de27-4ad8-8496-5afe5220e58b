/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface SocialDynamicsComplaintItemDto
 */
export interface SocialDynamicsComplaintItemDto {
    /**
     * 
     * @type {number}
     * @memberof SocialDynamicsComplaintItemDto
     */
    id?: number;
    /**
     * 动态ID
     * @type {number}
     * @memberof SocialDynamicsComplaintItemDto
     */
    dynamicsInfoId?: number;
    /**
     * 投诉人ID
     * @type {number}
     * @memberof SocialDynamicsComplaintItemDto
     */
    jobSeekerId?: number;
    /**
     * 
     * @type {number}
     * @memberof SocialDynamicsComplaintItemDto
     */
    enterpriseID?: number;
    /**
     * 投诉类型
     * @type {string}
     * @memberof SocialDynamicsComplaintItemDto
     */
    complaintTypes?: string | null;
    /**
     * 投诉时间
     * @type {Date}
     * @memberof SocialDynamicsComplaintItemDto
     */
    createTime?: Date;
    /**
     * 描述
     * @type {string}
     * @memberof SocialDynamicsComplaintItemDto
     */
    contents?: string | null;
    /**
     * 是否已处理
     * @type {boolean}
     * @memberof SocialDynamicsComplaintItemDto
     */
    isDeal?: boolean;
    /**
     * 唯一标识
     * @type {string}
     * @memberof SocialDynamicsComplaintItemDto
     */
    dynamicsInfoGuid?: string;
    /**
     * 动态标题
     * @type {string}
     * @memberof SocialDynamicsComplaintItemDto
     */
    title?: string | null;
    /**
     * 动态属于哪个企业
     * @type {string}
     * @memberof SocialDynamicsComplaintItemDto
     */
    dynamicsInfoEnterpriseName?: string | null;
    /**
     * 回复内容
     * @type {string}
     * @memberof SocialDynamicsComplaintItemDto
     */
    replyContent?: string | null;
    /**
     * 回复时间
     * @type {Date}
     * @memberof SocialDynamicsComplaintItemDto
     */
    dealTime?: Date | null;
}
