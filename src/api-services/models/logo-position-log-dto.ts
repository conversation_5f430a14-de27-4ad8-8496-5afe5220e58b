/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexPositionRecordType } from './index-position-record-type';
import { PositionClickFrom } from './position-click-from';
/**
 * 
 * @export
 * @interface LogoPositionLogDto
 */
export interface LogoPositionLogDto {
    /**
     * LogoID
     * @type {number}
     * @memberof LogoPositionLogDto
     */
    logoID?: number;
    /**
     * 职位ID，目前暂时填空
     * @type {number}
     * @memberof LogoPositionLogDto
     */
    positionID?: number | null;
    /**
     * 文章职位ID
     * @type {number}
     * @memberof LogoPositionLogDto
     */
    articlePositionId?: number;
    /**
     * 
     * @type {IndexPositionRecordType}
     * @memberof LogoPositionLogDto
     */
    recordType?: IndexPositionRecordType;
    /**
     * 邀约聊ID
     * @type {number}
     * @memberof LogoPositionLogDto
     */
    inviChatRecordId?: number | null;
    /**
     * 
     * @type {PositionClickFrom}
     * @memberof LogoPositionLogDto
     */
    positionClickFrom?: PositionClickFrom;
}
