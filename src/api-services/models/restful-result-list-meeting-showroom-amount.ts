/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { MeetingShowroomAmount } from './meeting-showroom-amount';
/**
 * 
 * @export
 * @interface RestfulResultListMeetingShowroomAmount
 */
export interface RestfulResultListMeetingShowroomAmount {
    /**
     * 
     * @type {number}
     * @memberof RestfulResultListMeetingShowroomAmount
     */
    code?: number | null;
    /**
     * 
     * @type {Array<MeetingShowroomAmount>}
     * @memberof RestfulResultListMeetingShowroomAmount
     */
    data?: Array<MeetingShowroomAmount> | null;
    /**
     * 
     * @type {any}
     * @memberof RestfulResultListMeetingShowroomAmount
     */
    message?: any | null;
    /**
     * 
     * @type {Date}
     * @memberof RestfulResultListMeetingShowroomAmount
     */
    now?: Date;
}
