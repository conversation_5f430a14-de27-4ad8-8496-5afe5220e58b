/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface IndexPositionListItemOriginal
 */
export interface IndexPositionListItemOriginal {
    /**
     * 跟踪guid
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    trackingGuid?: string | null;
    /**
     * 职位ID
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionID?: number;
    /**
     * 职位名称
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    positionName?: string | null;
    /**
     * 职位名称(索引不分词保存)
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    positionNameCopy?: string | null;
    /**
     * 职位guid
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    positionGuid?: string;
    /**
     * 企业名称
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    enterpriseName?: string | null;
    /**
     * 企业ID
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    enterpriseID?: number;
    /**
     * 企业Guid
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    enterpriseGuid?: string | null;
    /**
     * 招聘人数
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    enterpriseEmployeeNumber?: number;
    /**
     * 经度
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionZoomX2?: number | null;
    /**
     * 纬度
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionZoomY2?: number | null;
    /**
     * 招聘人数
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionAmount?: number;
    /**
     * 工作性质
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    workProperty?: number;
    /**
     * 单位性质
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    enterpriseProperty?: number;
    /**
     * 学历要求
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    requirementOfEducationDegree?: number;
    /**
     * 工龄要求
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    requirementOfWorkAge?: number;
    /**
     * 薪资
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    payPackage?: number;
    /**
     * 薪资文字
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    payPackageName?: string | null;
    /**
     * 发布日
     * @type {Date}
     * @memberof IndexPositionListItemOriginal
     */
    publishTime?: Date;
    /**
     * 更新日期
     * @type {Date}
     * @memberof IndexPositionListItemOriginal
     */
    publishDate?: Date;
    /**
     * 首发时间
     * @type {Date}
     * @memberof IndexPositionListItemOriginal
     */
    firstPublishTime?: Date;
    /**
     * 是否急聘
     * @type {boolean}
     * @memberof IndexPositionListItemOriginal
     */
    emergencyRrecruitmentFlag?: boolean;
    /**
     * 是否毕业生
     * @type {boolean}
     * @memberof IndexPositionListItemOriginal
     */
    isReceiveGraduate?: boolean;
    /**
     * 职位描述
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    positionDescription?: string | null;
    /**
     * 网联ID
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    enterpriseDistrictID?: number;
    /**
     * 福利
     * @type {Array<number>}
     * @memberof IndexPositionListItemOriginal
     */
    positionWelfareIDs?: Array<number> | null;
    /**
     * 职称要求
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    requirementOfWorkTitle?: number;
    /**
     * 年龄要求
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    requirementOfLowAge?: number;
    /**
     * 年龄要求
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    requirementOfHighAge?: number;
    /**
     * 语言
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    languageRequirement1?: number;
    /**
     * 语言
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    languageRequirement2?: number;
    /**
     * 语言
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    languageRequirement3?: number;
    /**
     * 语言
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    languageDegree1?: number;
    /**
     * 语言
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    languageDegree2?: number;
    /**
     * 语言
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    languageDegree3?: number;
    /**
     * 工作地
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    workDistrict1?: number;
    /**
     * 工作地
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    workDistrict2?: number;
    /**
     * 工作地
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    workDistrict3?: number;
    /**
     * 工作地
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    workDistrict4?: number;
    /**
     * 工作地
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    workDistrict5?: number;
    /**
     * 行业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionIndustryID5?: number;
    /**
     * 行业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionIndustryID4?: number;
    /**
     * 行业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionIndustryID2?: number;
    /**
     * 行业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionIndustryID3?: number;
    /**
     * 行业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionIndustryID1?: number;
    /**
     * 职能
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionCareerID4?: number;
    /**
     * 职能
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionCareerID5?: number;
    /**
     * 职能
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionCareerID3?: number;
    /**
     * 职能
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionCareerID2?: number;
    /**
     * 职能
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionCareerID1?: number;
    /**
     * 专业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    educationSpecialty1?: number;
    /**
     * 专业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    educationSpecialty2?: number;
    /**
     * 专业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    educationSpecialty3?: number;
    /**
     * 专业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    educationSpecialty4?: number;
    /**
     * 专业
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    educationSpecialty5?: number;
    /**
     * 工作地
     * @type {Array<number>}
     * @memberof IndexPositionListItemOriginal
     */
    workDistrict?: Array<number> | null;
    /**
     * 行业
     * @type {Array<number>}
     * @memberof IndexPositionListItemOriginal
     */
    positionIndustry?: Array<number> | null;
    /**
     * 职能
     * @type {Array<number>}
     * @memberof IndexPositionListItemOriginal
     */
    positionCareer?: Array<number> | null;
    /**
     * 专业
     * @type {Array<number>}
     * @memberof IndexPositionListItemOriginal
     */
    educationSpecialty?: Array<number> | null;
    /**
     * 回复率
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    replyRatio?: number;
    /**
     * 投递率
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    deliveryRatio?: number;
    /**
     * 企业是否在线
     * @type {boolean}
     * @memberof IndexPositionListItemOriginal
     */
    online?: boolean;
    /**
     * 经度
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionZoomX?: number | null;
    /**
     * 纬度
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionZoomY?: number | null;
    /**
     * 职位排序
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    positionSort?: number;
    /**
     * 是否值聊
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    zhiliao?: number;
    /**
     * 文档的版本号
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    version?: number;
    /**
     * 办公地坐标
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    location?: string | null;
    /**
     * 操作
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    operation?: string | null;
    /**
     * 分数
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    score?: number;
    /**
     * 距离
     * @type {string}
     * @memberof IndexPositionListItemOriginal
     */
    distance?: string | null;
    /**
     * 类型：0:原获取记录 1:文章广告推送 20230523
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    recordType?: number;
    /**
     * 曝光业务表的主键 [Voodoo_Enterprise].[dbo].[InvitationChatRecord]
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    inviChatRecordId?: number;
    /**
     * 曝光量
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    exposureCount?: number;
    /**
     * 曝光点击量
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    exposureClickCount?: number;
    /**
     * 是否曝光中
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    isExposing?: number;
    /**
     * 曝光开始时间
     * @type {Date}
     * @memberof IndexPositionListItemOriginal
     */
    exposureBeginTime?: Date;
    /**
     * 曝光结束时间
     * @type {Date}
     * @memberof IndexPositionListItemOriginal
     */
    exposureEndTime?: Date;
    /**
     * 
     * @type {number}
     * @memberof IndexPositionListItemOriginal
     */
    isSocialPosition?: number;
}
