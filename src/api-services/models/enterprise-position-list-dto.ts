/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { AppEnterprisePositionListOption } from './app-enterprise-position-list-option';
import { PositionItem } from './position-item';
/**
 * 
 * @export
 * @interface EnterprisePositionListDto
 */
export interface EnterprisePositionListDto {
    /**
     * 职位列表
     * @type {Array<PositionItem>}
     * @memberof EnterprisePositionListDto
     */
    positionList?: Array<PositionItem> | null;
    /**
     * 当前企业总职位数
     * @type {number}
     * @memberof EnterprisePositionListDto
     */
    positionNum?: number;
    /**
     * 薪资列表
     * @type {Array<AppEnterprisePositionListOption>}
     * @memberof EnterprisePositionListDto
     */
    paypackage?: Array<AppEnterprisePositionListOption> | null;
    /**
     * 职能列表
     * @type {Array<AppEnterprisePositionListOption>}
     * @memberof EnterprisePositionListDto
     */
    positionTypes?: Array<AppEnterprisePositionListOption> | null;
    /**
     * 工作地列表
     * @type {Array<AppEnterprisePositionListOption>}
     * @memberof EnterprisePositionListDto
     */
    workPlaces?: Array<AppEnterprisePositionListOption> | null;
}
