/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ZixunListItemDto } from './zixun-list-item-dto';
/**
 * 
 * @export
 * @interface PagedListZixunListItemDto
 */
export interface PagedListZixunListItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListZixunListItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListZixunListItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListZixunListItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListZixunListItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<ZixunListItemDto>}
     * @memberof PagedListZixunListItemDto
     */
    items?: Array<ZixunListItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListZixunListItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListZixunListItemDto
     */
    hasNextPages?: boolean;
}
