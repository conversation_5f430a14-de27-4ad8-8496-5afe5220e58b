/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { ConsultationItemOutput } from './consultation-item-output';
/**
 * 
 * @export
 * @interface PagedListConsultationItemOutput
 */
export interface PagedListConsultationItemOutput {
    /**
     * 
     * @type {number}
     * @memberof PagedListConsultationItemOutput
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListConsultationItemOutput
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListConsultationItemOutput
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListConsultationItemOutput
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<ConsultationItemOutput>}
     * @memberof PagedListConsultationItemOutput
     */
    items?: Array<ConsultationItemOutput> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListConsultationItemOutput
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListConsultationItemOutput
     */
    hasNextPages?: boolean;
}
