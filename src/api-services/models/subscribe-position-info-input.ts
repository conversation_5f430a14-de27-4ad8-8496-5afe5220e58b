/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 
 * @export
 * @interface SubscribePositionInfoInput
 */
export interface SubscribePositionInfoInput {
    /**
     * 订阅关键字
     * @type {string}
     * @memberof SubscribePositionInfoInput
     */
    keyWord: string;
    /**
     * 薪资范围
     * @type {Array<string>}
     * @memberof SubscribePositionInfoInput
     */
    payment?: Array<string> | null;
    /**
     * 学历要求
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    requirementOfEducationDegree?: Array<number> | null;
    /**
     * 经验要求
     * @type {string}
     * @memberof SubscribePositionInfoInput
     */
    workAge?: string | null;
    /**
     * 单位性质
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    enterpriseProperty?: Array<number> | null;
    /**
     * 公司福利
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    welfare?: Array<number> | null;
    /**
     * 招聘类型
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    workProperty?: Array<number> | null;
    /**
     * 是否接收毕业生
     * @type {boolean}
     * @memberof SubscribePositionInfoInput
     */
    isReceiveGraduate?: boolean;
    /**
     * 只看急聘职位
     * @type {boolean}
     * @memberof SubscribePositionInfoInput
     */
    emergency?: boolean | null;
    /**
     * 行业类型
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    positionIndustry?: Array<number> | null;
    /**
     * 职位类型
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    positionCaree?: Array<number> | null;
    /**
     * 工作地
     * @type {Array<number>}
     * @memberof SubscribePositionInfoInput
     */
    workPlace?: Array<number> | null;
}
