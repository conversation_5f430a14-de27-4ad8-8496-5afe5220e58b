/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { DisoverListItemDto } from './disover-list-item-dto';
/**
 * 
 * @export
 * @interface PagedListDisoverListItemDto
 */
export interface PagedListDisoverListItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListDisoverListItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListDisoverListItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListDisoverListItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListDisoverListItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<DisoverListItemDto>}
     * @memberof PagedListDisoverListItemDto
     */
    items?: Array<DisoverListItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListDisoverListItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListDisoverListItemDto
     */
    hasNextPages?: boolean;
}
