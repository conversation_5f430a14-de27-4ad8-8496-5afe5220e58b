/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { CommunityAuditState } from './community-audit-state';
import { CommunityDynamicsImageInfo } from './community-dynamics-image-info';
import { CommunityDynamicsType } from './community-dynamics-type';
import { DepartmentPositions } from './department-positions';
import { DynamicsRollPage } from './dynamics-roll-page';
import { EnterpriseWithPositions } from './enterprise-with-positions';
/**
 * 发现社区动态输出数据模型
 * @export
 * @interface CommunityDynamicsInfoOutput
 */
export interface CommunityDynamicsInfoOutput {
    /**
     * 唯一标识
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    dynamicsInfoGuid?: string;
    /**
     * 动态发送者
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    sender?: string | null;
    /**
     * 是否运营账号
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    isOperater?: boolean;
    /**
     * 动态发送者的GUID(企业的或者求职者的)
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    senderGuid?: string;
    /**
     * 动态作者头像URL
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    senderAvatarUrl?: string | null;
    /**
     * 发送时间
     * @type {Date}
     * @memberof CommunityDynamicsInfoOutput
     */
    sendTime?: Date;
    /**
     * 发送时间中文显示
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    chnSendTime?: string | null;
    /**
     * 标题
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    title?: string | null;
    /**
     * 文本内容
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    content?: string | null;
    /**
     * 动态发出的客户端来源，保存RegFrom枚举值
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    addFrom?: number;
    /**
     * 包含的话题数组
     * @type {Array<string>}
     * @memberof CommunityDynamicsInfoOutput
     */
    topics?: Array<string> | null;
    /**
     * 点赞数量
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    likes?: number;
    /**
     * 最后更新时间
     * @type {Date}
     * @memberof CommunityDynamicsInfoOutput
     */
    updatedTime?: Date | null;
    /**
     * 收藏数量
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    collections?: number;
    /**
     * 评论数量
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    comments?: number;
    /**
     * 
     * @type {CommunityDynamicsType}
     * @memberof CommunityDynamicsInfoOutput
     */
    type?: CommunityDynamicsType;
    /**
     * 图片清单
     * @type {Array<CommunityDynamicsImageInfo>}
     * @memberof CommunityDynamicsInfoOutput
     */
    images?: Array<CommunityDynamicsImageInfo> | null;
    /**
     * 视频播放地址
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    videoUrl?: string | null;
    /**
     * 视频vid
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    vid?: number;
    /**
     * 视频ObjectName
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    objectName?: string | null;
    /**
     * 视频OriginFileName
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    originFileName?: string | null;
    /**
     * 视频OSnapshotOffset
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    snapshotOffset?: number;
    /**
     * 视频播放时长（单位：秒）
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    duration?: number;
    /**
     * 视频截图URL
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    snapshotUrl?: string | null;
    /**
     * 视频封面宽度
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    snapshotWidth?: number;
    /**
     * 视频封面高度
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    snapshotHeight?: number;
    /**
     * 当前求职者是否点赞此动态
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    liked?: boolean;
    /**
     * 当前求职者是否收藏此动态
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    collected?: boolean;
    /**
     * 当前求职者是否已关注企业
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    followed?: boolean;
    /**
     * 是否置顶
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    isTop?: boolean | null;
    /**
     * 置顶顺序
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    topOrder?: number | null;
    /**
     * 
     * @type {CommunityAuditState}
     * @memberof CommunityDynamicsInfoOutput
     */
    auditState?: CommunityAuditState;
    /**
     * 动态设定是否展示
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    isShow?: boolean;
    /**
     * 动态定位地点
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    location?: string | null;
    /**
     * 动态位置信息经度
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    zoomX?: number | null;
    /**
     * 动态位置信息纬度
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    zoomY?: number | null;
    /**
     * 动态是否包含多家公司的推荐职位
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    isMultiEnterprisesRecommend?: boolean;
    /**
     * 动态里包含的单公司分部门推荐职位列表
     * @type {Array<DepartmentPositions>}
     * @memberof CommunityDynamicsInfoOutput
     */
    recommendPositions?: Array<DepartmentPositions> | null;
    /**
     * 动态里包含的多公司不分部门推荐职位列表
     * @type {Array<EnterpriseWithPositions>}
     * @memberof CommunityDynamicsInfoOutput
     */
    multiEnterprisesRecommendPositions?: Array<EnterpriseWithPositions> | null;
    /**
     * 职位动态对应的职位ID
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    promotionPositionId?: number;
    /**
     * 职位动态对应的职位GUID
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    promotionPositionGuid?: string;
    /**
     * 企业所属行业
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    enterpriseIndustry?: string | null;
    /**
     * 企业雇员规模
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    employeeNumber?: string | null;
    /**
     * 审核不通过原因
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    auditFailedReason?: string | null;
    /**
     * 资讯类型特有昵称
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    nikeName?: string | null;
    /**
     * 资讯类型特有来源昵称
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    fromName?: string | null;
    /**
     * 浏览量显示用
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    browseCount?: string | null;
    /**
     * 默认0 为企业发布，1为运营，2为资讯的发布
     * @type {number}
     * @memberof CommunityDynamicsInfoOutput
     */
    fromTypeId?: number | null;
    /**
     * 企业招聘业务是否过期
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    isBusinessExpiration?: boolean;
    /**
     * 是否允许评论
     * @type {boolean}
     * @memberof CommunityDynamicsInfoOutput
     */
    canComment?: boolean;
    /**
     * 外链
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    outerLink?: string | null;
    /**
     * 外链标题
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    outerLinkTitle?: string | null;
    /**
     * 外链图标
     * @type {string}
     * @memberof CommunityDynamicsInfoOutput
     */
    outerLinkIcon?: string | null;
    /**
     * 
     * @type {DynamicsRollPage}
     * @memberof CommunityDynamicsInfoOutput
     */
    rollPageInfo?: DynamicsRollPage;
}
