/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 发现社区动态滚动页面位置信息
 * @export
 * @interface DynamicsRollPage
 */
export interface DynamicsRollPage {
    /**
     * 滑动token
     * @type {string}
     * @memberof DynamicsRollPage
     */
    rollToken?: string | null;
    /**
     * 滚动上一个动态索引位置
     * @type {number}
     * @memberof DynamicsRollPage
     */
    preRollIndex?: number;
    /**
     * 滚动上一个动态guid
     * @type {string}
     * @memberof DynamicsRollPage
     */
    preSocialGuid?: string | null;
    /**
     * 滚动下一个动态索引位置
     * @type {number}
     * @memberof DynamicsRollPage
     */
    nextRollIndex?: number;
    /**
     * 滚动下一个动态guid
     * @type {string}
     * @memberof DynamicsRollPage
     */
    nextSocialGuid?: string | null;
    /**
     * 上XX（10）条
     * @type {Array<string>}
     * @memberof DynamicsRollPage
     */
    preSocialGuidList?: Array<string> | null;
    /**
     * 下XX（10）条
     * @type {Array<string>}
     * @memberof DynamicsRollPage
     */
    nextSocialGuidList?: Array<string> | null;
}
