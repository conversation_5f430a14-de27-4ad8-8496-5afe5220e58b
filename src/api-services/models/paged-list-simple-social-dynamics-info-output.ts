/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { SimpleSocialDynamicsInfoOutput } from './simple-social-dynamics-info-output';
/**
 * 
 * @export
 * @interface PagedListSimpleSocialDynamicsInfoOutput
 */
export interface PagedListSimpleSocialDynamicsInfoOutput {
    /**
     * 
     * @type {number}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<SimpleSocialDynamicsInfoOutput>}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    items?: Array<SimpleSocialDynamicsInfoOutput> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListSimpleSocialDynamicsInfoOutput
     */
    hasNextPages?: boolean;
}
