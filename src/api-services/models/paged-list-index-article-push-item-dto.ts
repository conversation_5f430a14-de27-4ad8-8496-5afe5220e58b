/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexArticlePushItemDto } from './index-article-push-item-dto';
/**
 * 
 * @export
 * @interface PagedListIndexArticlePushItemDto
 */
export interface PagedListIndexArticlePushItemDto {
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexArticlePushItemDto
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexArticlePushItemDto
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexArticlePushItemDto
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexArticlePushItemDto
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<IndexArticlePushItemDto>}
     * @memberof PagedListIndexArticlePushItemDto
     */
    items?: Array<IndexArticlePushItemDto> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexArticlePushItemDto
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexArticlePushItemDto
     */
    hasNextPages?: boolean;
}
