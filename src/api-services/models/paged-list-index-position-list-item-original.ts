/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexPositionListItemOriginal } from './index-position-list-item-original';
/**
 * 
 * @export
 * @interface PagedListIndexPositionListItemOriginal
 */
export interface PagedListIndexPositionListItemOriginal {
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    pageIndex?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    pageSize?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    totalCount?: number;
    /**
     * 
     * @type {number}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    totalPages?: number;
    /**
     * 
     * @type {Array<IndexPositionListItemOriginal>}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    items?: Array<IndexPositionListItemOriginal> | null;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    hasPrevPages?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof PagedListIndexPositionListItemOriginal
     */
    hasNextPages?: boolean;
}
