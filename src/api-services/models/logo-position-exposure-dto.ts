/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import { IndexPositionRecordType } from './index-position-record-type';
import { PositionClickFrom } from './position-click-from';
import { PositionExposureDto } from './position-exposure-dto';
/**
 * 
 * @export
 * @interface LogoPositionExposureDto
 */
export interface LogoPositionExposureDto {
    /**
     * LogoID
     * @type {number}
     * @memberof LogoPositionExposureDto
     */
    logoID?: number;
    /**
     * 文章职位ID
     * @type {number}
     * @memberof LogoPositionExposureDto
     */
    articlePositionId?: number;
    /**
     * 
     * @type {IndexPositionRecordType}
     * @memberof LogoPositionExposureDto
     */
    recordType?: IndexPositionRecordType;
    /**
     * 
     * @type {Array<PositionExposureDto>}
     * @memberof LogoPositionExposureDto
     */
    positionExposures?: Array<PositionExposureDto> | null;
    /**
     * 
     * @type {PositionClickFrom}
     * @memberof LogoPositionExposureDto
     */
    positionClickFrom?: PositionClickFrom;
}
