/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { EnumLoginFrom } from '../models';
import { RestfulResultBoolean } from '../models';
import { RestfulResultCommunityDynamicsInfoOutput } from '../models';
import { RestfulResultCommunityHomeUnreadMessagePromptRecord } from '../models';
import { RestfulResultDynamicsRollPage } from '../models';
import { RestfulResultEnterpriseSocialDynamicsBaseInfoOutput } from '../models';
import { RestfulResultInt32 } from '../models';
import { RestfulResultListSocialDynamicsComplaintTypeModel } from '../models';
import { RestfulResultPagedListSimpleSocialDynamicsInfoOutput } from '../models';
import { RestfulResultPagedListSocialDynamicsComment } from '../models';
import { RestfulResultPagedListSocialDynamicsComplaintItemDto } from '../models';
import { RestfulResultPagedListSocialDynamicsInfoOutput } from '../models';
import { RestfulResultPagerSimpleCommunityDynamicsInfoOutput } from '../models';
import { RestfulResultSocialDynamicsInfoOutput } from '../models';
import { RestfulResultSocialDynamicsShareUrlOutput } from '../models';
import { SocialDynamicsComplaintAddModel } from '../models';
import { SocialDynamicsViewLogModel } from '../models';
/**
 * SocialdynamicsApi - axios parameter creator
 * @export
 */
export const SocialdynamicsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 提交投诉
         * @param {SocialDynamicsComplaintAddModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsAddcomplaintPost: async (body?: SocialDynamicsComplaintAddModel, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/addcomplaint`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取评论数量
         * @param {string} hostguid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsCommentcountHostguidGet: async (hostguid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'hostguid' is not null or undefined
            if (hostguid === null || hostguid === undefined) {
                throw new RequiredError('hostguid','Required parameter hostguid was null or undefined when calling apiSocialdynamicsCommentcountHostguidGet.');
            }
            const localVarPath = `/api/socialdynamics/commentcount/{hostguid}`
                .replace(`{${"hostguid"}}`, encodeURIComponent(String(hostguid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取我的投诉列表
         * @param {number} [page] 
         * @param {number} [pagesize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsComplaintlistGet: async (page?: number, pagesize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/complaintlist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pagesize !== undefined) {
                localVarQueryParameter['pagesize'] = pagesize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 投诉类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsComplainttypelistGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/complainttypelist`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据企业的GUID分页获取企业的动态，只返回展示的并且审核通过的动态
         * @param {string} enterpriseGuid 企业GUID
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataByenterpriseEnterpriseGuidGet: async (enterpriseGuid: string, pageIndex?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'enterpriseGuid' is not null or undefined
            if (enterpriseGuid === null || enterpriseGuid === undefined) {
                throw new RequiredError('enterpriseGuid','Required parameter enterpriseGuid was null or undefined when calling apiSocialdynamicsDataByenterpriseEnterpriseGuidGet.');
            }
            const localVarPath = `/api/socialdynamics/data/byenterprise/{enterpriseGuid}`
                .replace(`{${"enterpriseGuid"}}`, encodeURIComponent(String(enterpriseGuid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询当前求职者关注的企业的详细社交动态
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataByfollowsGet: async (pageIndex?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/byfollows`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据话题查询简化社交动态
         * @param {string} [topic] 话题
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataBytopicGet: async (topic?: string, pageIndex?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/bytopic`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (topic !== undefined) {
                localVarQueryParameter['topic'] = topic;
            }

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据动态GUID查询单条发现社区动态滚动页面位置信息
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 平台
         * @param {string} [rollToken] 滚动token
         * @param {number} [rollIndex] 当前动态GUID位置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataDynamicsrollpageGuidGet: async (guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiSocialdynamicsDataDynamicsrollpageGuidGet.');
            }
            const localVarPath = `/api/socialdynamics/data/dynamicsrollpage/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (platform !== undefined) {
                localVarQueryParameter['platform'] = platform;
            }

            if (rollToken !== undefined) {
                localVarQueryParameter['rollToken'] = rollToken;
            }

            if (rollIndex !== undefined) {
                localVarQueryParameter['rollIndex'] = rollIndex;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据企业GUID查询企业动态主页的基本信息
         * @param {string} enterpriseGuid 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet: async (enterpriseGuid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'enterpriseGuid' is not null or undefined
            if (enterpriseGuid === null || enterpriseGuid === undefined) {
                throw new RequiredError('enterpriseGuid','Required parameter enterpriseGuid was null or undefined when calling apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet.');
            }
            const localVarPath = `/api/socialdynamics/data/enterprisebaseinfo/{enterpriseGuid}`
                .replace(`{${"enterpriseGuid"}}`, encodeURIComponent(String(enterpriseGuid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据动态GUID查询单条动态
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataGuidGet: async (guid: string, platform?: EnumLoginFrom, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiSocialdynamicsDataGuidGet.');
            }
            const localVarPath = `/api/socialdynamics/data/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (platform !== undefined) {
                localVarQueryParameter['platform'] = platform;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询新机会
         * @param {number} [pageIndex] 页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限定为20
         * @param {number} [type] 0：新机会，1：搜索结果页推荐职位
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataNewofferGet: async (pageIndex?: number, pageSize?: number, type?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/newoffer`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (type !== undefined) {
                localVarQueryParameter['type'] = type;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 按照动态发布时间降序分页查询简化社交动态
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataPageGet: async (pageIndex?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据动态GUID查询单条动态详情 20231129  说明 职位动态返回职位的GUID，客户端根据职位的GUID去查询现成的接口获取职位的详细信息
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 平台
         * @param {string} [rollToken] 滚动token
         * @param {number} [rollIndex] 当前动态GUID位置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataRollbydynamicsguidGuidGet: async (guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiSocialdynamicsDataRollbydynamicsguidGuidGet.');
            }
            const localVarPath = `/api/socialdynamics/data/rollbydynamicsguid/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (platform !== undefined) {
                localVarQueryParameter['platform'] = platform;
            }

            if (rollToken !== undefined) {
                localVarQueryParameter['rollToken'] = rollToken;
            }

            if (rollIndex !== undefined) {
                localVarQueryParameter['rollIndex'] = rollIndex;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
         * @param {string} [keywords] 关键词
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {boolean} [highlight] 是否加高亮HTML
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataSearchGet: async (keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keywords !== undefined) {
                localVarQueryParameter['keywords'] = keywords;
            }

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['highlight'] = highlight;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据职位名称关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
         * @param {string} [keywords] 职位名称关键词
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {boolean} [highlight] 是否加高亮HTML
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataSearchbypositionGet: async (keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/searchbyposition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keywords !== undefined) {
                localVarQueryParameter['keywords'] = keywords;
            }

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['highlight'] = highlight;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取动态分享链接
         * @param {string} guid 动态GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataShareurlGuidGet: async (guid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiSocialdynamicsDataShareurlGuidGet.');
            }
            const localVarPath = `/api/socialdynamics/data/shareurl/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 20231122 推荐瀑布流查询
         * @param {number} [pageIndex] 页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限定为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsDataWaterfallrecomGet: async (pageIndex?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/data/waterfallrecom`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 定位评论点赞的列表  获取评论列表
         * @param {string} [hostGuid] 
         * @param {number} [commentId] 点赞评论的那条评论id
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {number} [hot] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsInteractionCommentlistbylocationGet: async (hostGuid?: string, commentId?: number, page?: number, pageSize?: number, hot?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/interaction/commentlistbylocation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (hostGuid !== undefined) {
                localVarQueryParameter['HostGuid'] = hostGuid;
            }

            if (commentId !== undefined) {
                localVarQueryParameter['CommentId'] = commentId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (hot !== undefined) {
                localVarQueryParameter['Hot'] = hot;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取初始化评论
         * @param {string} [hostGuid] 文章或者动态的guid
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {number} [hot] 1是热点，0是按时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsInteractionDefaultcommentsGet: async (hostGuid?: string, page?: number, pageSize?: number, hot?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/interaction/defaultcomments`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (hostGuid !== undefined) {
                localVarQueryParameter['HostGuid'] = hostGuid;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (hot !== undefined) {
                localVarQueryParameter['hot'] = hot;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取更多折叠的评论
         * @param {number} [commentId] 
         * @param {string} [hostGuid] 文章或者动态的guid
         * @param {number} [pageIndex] 
         * @param {number} [pageSize] 
         * @param {number} [hasCommentId] 当前已显示的首个评论
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsInteractionMorecommentsGet: async (commentId?: number, hostGuid?: string, pageIndex?: number, pageSize?: number, hasCommentId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/interaction/morecomments`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (commentId !== undefined) {
                localVarQueryParameter['commentId'] = commentId;
            }

            if (hostGuid !== undefined) {
                localVarQueryParameter['HostGuid'] = hostGuid;
            }

            if (pageIndex !== undefined) {
                localVarQueryParameter['pageIndex'] = pageIndex;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (hasCommentId !== undefined) {
                localVarQueryParameter['hasCommentId'] = hasCommentId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询未读的新机会消息数量、互动消息和系统消息数量之和
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsUnreadmsgpromptGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/unreadmsgprompt`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 添加浏览相关日志  --进入页面请不要调用接口
         * @param {SocialDynamicsViewLogModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSocialdynamicsViewlogaddPost: async (body?: SocialDynamicsViewLogModel, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/socialdynamics/viewlogadd`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SocialdynamicsApi - functional programming interface
 * @export
 */
export const SocialdynamicsApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 提交投诉
         * @param {SocialDynamicsComplaintAddModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsAddcomplaintPost(body?: SocialDynamicsComplaintAddModel, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBoolean>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsAddcomplaintPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取评论数量
         * @param {string} hostguid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsCommentcountHostguidGet(hostguid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultInt32>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsCommentcountHostguidGet(hostguid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取我的投诉列表
         * @param {number} [page] 
         * @param {number} [pagesize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsComplaintlistGet(page?: number, pagesize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComplaintItemDto>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsComplaintlistGet(page, pagesize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 投诉类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsComplainttypelistGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListSocialDynamicsComplaintTypeModel>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsComplainttypelistGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据企业的GUID分页获取企业的动态，只返回展示的并且审核通过的动态
         * @param {string} enterpriseGuid 企业GUID
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataByenterpriseEnterpriseGuidGet(enterpriseGuid: string, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataByenterpriseEnterpriseGuidGet(enterpriseGuid, pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 查询当前求职者关注的企业的详细社交动态
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataByfollowsGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataByfollowsGet(pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据话题查询简化社交动态
         * @param {string} [topic] 话题
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataBytopicGet(topic?: string, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSimpleSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataBytopicGet(topic, pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据动态GUID查询单条发现社区动态滚动页面位置信息
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 平台
         * @param {string} [rollToken] 滚动token
         * @param {number} [rollIndex] 当前动态GUID位置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataDynamicsrollpageGuidGet(guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultDynamicsRollPage>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataDynamicsrollpageGuidGet(guid, platform, rollToken, rollIndex, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据企业GUID查询企业动态主页的基本信息
         * @param {string} enterpriseGuid 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet(enterpriseGuid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterpriseSocialDynamicsBaseInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet(enterpriseGuid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据动态GUID查询单条动态
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataGuidGet(guid: string, platform?: EnumLoginFrom, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataGuidGet(guid, platform, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 查询新机会
         * @param {number} [pageIndex] 页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限定为20
         * @param {number} [type] 0：新机会，1：搜索结果页推荐职位
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataNewofferGet(pageIndex?: number, pageSize?: number, type?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagerSimpleCommunityDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataNewofferGet(pageIndex, pageSize, type, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 按照动态发布时间降序分页查询简化社交动态
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataPageGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSimpleSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataPageGet(pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据动态GUID查询单条动态详情 20231129  说明 职位动态返回职位的GUID，客户端根据职位的GUID去查询现成的接口获取职位的详细信息
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 平台
         * @param {string} [rollToken] 滚动token
         * @param {number} [rollIndex] 当前动态GUID位置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataRollbydynamicsguidGuidGet(guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultCommunityDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataRollbydynamicsguidGuidGet(guid, platform, rollToken, rollIndex, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
         * @param {string} [keywords] 关键词
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {boolean} [highlight] 是否加高亮HTML
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataSearchGet(keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataSearchGet(keywords, pageIndex, pageSize, highlight, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据职位名称关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
         * @param {string} [keywords] 职位名称关键词
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {boolean} [highlight] 是否加高亮HTML
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataSearchbypositionGet(keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataSearchbypositionGet(keywords, pageIndex, pageSize, highlight, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取动态分享链接
         * @param {string} guid 动态GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataShareurlGuidGet(guid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSocialDynamicsShareUrlOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataShareurlGuidGet(guid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 20231122 推荐瀑布流查询
         * @param {number} [pageIndex] 页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限定为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataWaterfallrecomGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagerSimpleCommunityDynamicsInfoOutput>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsDataWaterfallrecomGet(pageIndex, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 定位评论点赞的列表  获取评论列表
         * @param {string} [hostGuid] 
         * @param {number} [commentId] 点赞评论的那条评论id
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {number} [hot] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsInteractionCommentlistbylocationGet(hostGuid?: string, commentId?: number, page?: number, pageSize?: number, hot?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsInteractionCommentlistbylocationGet(hostGuid, commentId, page, pageSize, hot, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取初始化评论
         * @param {string} [hostGuid] 文章或者动态的guid
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {number} [hot] 1是热点，0是按时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsInteractionDefaultcommentsGet(hostGuid?: string, page?: number, pageSize?: number, hot?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsInteractionDefaultcommentsGet(hostGuid, page, pageSize, hot, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取更多折叠的评论
         * @param {number} [commentId] 
         * @param {string} [hostGuid] 文章或者动态的guid
         * @param {number} [pageIndex] 
         * @param {number} [pageSize] 
         * @param {number} [hasCommentId] 当前已显示的首个评论
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsInteractionMorecommentsGet(commentId?: number, hostGuid?: string, pageIndex?: number, pageSize?: number, hasCommentId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsInteractionMorecommentsGet(commentId, hostGuid, pageIndex, pageSize, hasCommentId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 查询未读的新机会消息数量、互动消息和系统消息数量之和
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsUnreadmsgpromptGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultCommunityHomeUnreadMessagePromptRecord>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsUnreadmsgpromptGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 添加浏览相关日志  --进入页面请不要调用接口
         * @param {SocialDynamicsViewLogModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsViewlogaddPost(body?: SocialDynamicsViewLogModel, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBoolean>>> {
            const localVarAxiosArgs = await SocialdynamicsApiAxiosParamCreator(configuration).apiSocialdynamicsViewlogaddPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SocialdynamicsApi - factory interface
 * @export
 */
export const SocialdynamicsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 提交投诉
         * @param {SocialDynamicsComplaintAddModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsAddcomplaintPost(body?: SocialDynamicsComplaintAddModel, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBoolean>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsAddcomplaintPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取评论数量
         * @param {string} hostguid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsCommentcountHostguidGet(hostguid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultInt32>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsCommentcountHostguidGet(hostguid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取我的投诉列表
         * @param {number} [page] 
         * @param {number} [pagesize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsComplaintlistGet(page?: number, pagesize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComplaintItemDto>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsComplaintlistGet(page, pagesize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 投诉类型列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsComplainttypelistGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListSocialDynamicsComplaintTypeModel>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsComplainttypelistGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据企业的GUID分页获取企业的动态，只返回展示的并且审核通过的动态
         * @param {string} enterpriseGuid 企业GUID
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataByenterpriseEnterpriseGuidGet(enterpriseGuid: string, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataByenterpriseEnterpriseGuidGet(enterpriseGuid, pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询当前求职者关注的企业的详细社交动态
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataByfollowsGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataByfollowsGet(pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据话题查询简化社交动态
         * @param {string} [topic] 话题
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataBytopicGet(topic?: string, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSimpleSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataBytopicGet(topic, pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据动态GUID查询单条发现社区动态滚动页面位置信息
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 平台
         * @param {string} [rollToken] 滚动token
         * @param {number} [rollIndex] 当前动态GUID位置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataDynamicsrollpageGuidGet(guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultDynamicsRollPage>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataDynamicsrollpageGuidGet(guid, platform, rollToken, rollIndex, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据企业GUID查询企业动态主页的基本信息
         * @param {string} enterpriseGuid 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet(enterpriseGuid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterpriseSocialDynamicsBaseInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet(enterpriseGuid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据动态GUID查询单条动态
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataGuidGet(guid: string, platform?: EnumLoginFrom, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataGuidGet(guid, platform, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询新机会
         * @param {number} [pageIndex] 页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限定为20
         * @param {number} [type] 0：新机会，1：搜索结果页推荐职位
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataNewofferGet(pageIndex?: number, pageSize?: number, type?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagerSimpleCommunityDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataNewofferGet(pageIndex, pageSize, type, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 按照动态发布时间降序分页查询简化社交动态
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataPageGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSimpleSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataPageGet(pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据动态GUID查询单条动态详情 20231129  说明 职位动态返回职位的GUID，客户端根据职位的GUID去查询现成的接口获取职位的详细信息
         * @param {string} guid 动态GUID
         * @param {EnumLoginFrom} [platform] 平台
         * @param {string} [rollToken] 滚动token
         * @param {number} [rollIndex] 当前动态GUID位置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataRollbydynamicsguidGuidGet(guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultCommunityDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataRollbydynamicsguidGuidGet(guid, platform, rollToken, rollIndex, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
         * @param {string} [keywords] 关键词
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {boolean} [highlight] 是否加高亮HTML
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataSearchGet(keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataSearchGet(keywords, pageIndex, pageSize, highlight, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据职位名称关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
         * @param {string} [keywords] 职位名称关键词
         * @param {number} [pageIndex] 当前页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限制为20
         * @param {boolean} [highlight] 是否加高亮HTML
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataSearchbypositionGet(keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataSearchbypositionGet(keywords, pageIndex, pageSize, highlight, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取动态分享链接
         * @param {string} guid 动态GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataShareurlGuidGet(guid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSocialDynamicsShareUrlOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataShareurlGuidGet(guid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 20231122 推荐瀑布流查询
         * @param {number} [pageIndex] 页索引，从1开始
         * @param {number} [pageSize] 页大小，最大限定为20
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsDataWaterfallrecomGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagerSimpleCommunityDynamicsInfoOutput>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsDataWaterfallrecomGet(pageIndex, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 定位评论点赞的列表  获取评论列表
         * @param {string} [hostGuid] 
         * @param {number} [commentId] 点赞评论的那条评论id
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {number} [hot] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsInteractionCommentlistbylocationGet(hostGuid?: string, commentId?: number, page?: number, pageSize?: number, hot?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsInteractionCommentlistbylocationGet(hostGuid, commentId, page, pageSize, hot, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取初始化评论
         * @param {string} [hostGuid] 文章或者动态的guid
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {number} [hot] 1是热点，0是按时间
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsInteractionDefaultcommentsGet(hostGuid?: string, page?: number, pageSize?: number, hot?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsInteractionDefaultcommentsGet(hostGuid, page, pageSize, hot, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取更多折叠的评论
         * @param {number} [commentId] 
         * @param {string} [hostGuid] 文章或者动态的guid
         * @param {number} [pageIndex] 
         * @param {number} [pageSize] 
         * @param {number} [hasCommentId] 当前已显示的首个评论
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsInteractionMorecommentsGet(commentId?: number, hostGuid?: string, pageIndex?: number, pageSize?: number, hasCommentId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsInteractionMorecommentsGet(commentId, hostGuid, pageIndex, pageSize, hasCommentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询未读的新机会消息数量、互动消息和系统消息数量之和
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsUnreadmsgpromptGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultCommunityHomeUnreadMessagePromptRecord>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsUnreadmsgpromptGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 添加浏览相关日志  --进入页面请不要调用接口
         * @param {SocialDynamicsViewLogModel} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSocialdynamicsViewlogaddPost(body?: SocialDynamicsViewLogModel, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBoolean>> {
            return SocialdynamicsApiFp(configuration).apiSocialdynamicsViewlogaddPost(body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SocialdynamicsApi - object-oriented interface
 * @export
 * @class SocialdynamicsApi
 * @extends {BaseAPI}
 */
export class SocialdynamicsApi extends BaseAPI {
    /**
     * 
     * @summary 提交投诉
     * @param {SocialDynamicsComplaintAddModel} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsAddcomplaintPost(body?: SocialDynamicsComplaintAddModel, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBoolean>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsAddcomplaintPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取评论数量
     * @param {string} hostguid 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsCommentcountHostguidGet(hostguid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultInt32>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsCommentcountHostguidGet(hostguid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取我的投诉列表
     * @param {number} [page] 
     * @param {number} [pagesize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsComplaintlistGet(page?: number, pagesize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComplaintItemDto>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsComplaintlistGet(page, pagesize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 投诉类型列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsComplainttypelistGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListSocialDynamicsComplaintTypeModel>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsComplainttypelistGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据企业的GUID分页获取企业的动态，只返回展示的并且审核通过的动态
     * @param {string} enterpriseGuid 企业GUID
     * @param {number} [pageIndex] 当前页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限制为20
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataByenterpriseEnterpriseGuidGet(enterpriseGuid: string, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataByenterpriseEnterpriseGuidGet(enterpriseGuid, pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 查询当前求职者关注的企业的详细社交动态
     * @param {number} [pageIndex] 当前页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限制为20
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataByfollowsGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataByfollowsGet(pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据话题查询简化社交动态
     * @param {string} [topic] 话题
     * @param {number} [pageIndex] 当前页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限制为20
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataBytopicGet(topic?: string, pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSimpleSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataBytopicGet(topic, pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据动态GUID查询单条发现社区动态滚动页面位置信息
     * @param {string} guid 动态GUID
     * @param {EnumLoginFrom} [platform] 平台
     * @param {string} [rollToken] 滚动token
     * @param {number} [rollIndex] 当前动态GUID位置
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataDynamicsrollpageGuidGet(guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultDynamicsRollPage>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataDynamicsrollpageGuidGet(guid, platform, rollToken, rollIndex, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据企业GUID查询企业动态主页的基本信息
     * @param {string} enterpriseGuid 企业GUID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet(enterpriseGuid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterpriseSocialDynamicsBaseInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataEnterprisebaseinfoEnterpriseGuidGet(enterpriseGuid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据动态GUID查询单条动态
     * @param {string} guid 动态GUID
     * @param {EnumLoginFrom} [platform] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataGuidGet(guid: string, platform?: EnumLoginFrom, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataGuidGet(guid, platform, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 查询新机会
     * @param {number} [pageIndex] 页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限定为20
     * @param {number} [type] 0：新机会，1：搜索结果页推荐职位
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataNewofferGet(pageIndex?: number, pageSize?: number, type?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagerSimpleCommunityDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataNewofferGet(pageIndex, pageSize, type, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 按照动态发布时间降序分页查询简化社交动态
     * @param {number} [pageIndex] 当前页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限制为20
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataPageGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSimpleSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataPageGet(pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据动态GUID查询单条动态详情 20231129  说明 职位动态返回职位的GUID，客户端根据职位的GUID去查询现成的接口获取职位的详细信息
     * @param {string} guid 动态GUID
     * @param {EnumLoginFrom} [platform] 平台
     * @param {string} [rollToken] 滚动token
     * @param {number} [rollIndex] 当前动态GUID位置
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataRollbydynamicsguidGuidGet(guid: string, platform?: EnumLoginFrom, rollToken?: string, rollIndex?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultCommunityDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataRollbydynamicsguidGuidGet(guid, platform, rollToken, rollIndex, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
     * @param {string} [keywords] 关键词
     * @param {number} [pageIndex] 当前页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限制为20
     * @param {boolean} [highlight] 是否加高亮HTML
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataSearchGet(keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataSearchGet(keywords, pageIndex, pageSize, highlight, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据职位名称关键词分页查询所有企业的按发布时间降序排序的简化动态信息，只返回展示的并且审核通过的动态
     * @param {string} [keywords] 职位名称关键词
     * @param {number} [pageIndex] 当前页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限制为20
     * @param {boolean} [highlight] 是否加高亮HTML
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataSearchbypositionGet(keywords?: string, pageIndex?: number, pageSize?: number, highlight?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataSearchbypositionGet(keywords, pageIndex, pageSize, highlight, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取动态分享链接
     * @param {string} guid 动态GUID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataShareurlGuidGet(guid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSocialDynamicsShareUrlOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataShareurlGuidGet(guid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 20231122 推荐瀑布流查询
     * @param {number} [pageIndex] 页索引，从1开始
     * @param {number} [pageSize] 页大小，最大限定为20
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsDataWaterfallrecomGet(pageIndex?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagerSimpleCommunityDynamicsInfoOutput>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsDataWaterfallrecomGet(pageIndex, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 定位评论点赞的列表  获取评论列表
     * @param {string} [hostGuid] 
     * @param {number} [commentId] 点赞评论的那条评论id
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {number} [hot] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsInteractionCommentlistbylocationGet(hostGuid?: string, commentId?: number, page?: number, pageSize?: number, hot?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsInteractionCommentlistbylocationGet(hostGuid, commentId, page, pageSize, hot, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取初始化评论
     * @param {string} [hostGuid] 文章或者动态的guid
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {number} [hot] 1是热点，0是按时间
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsInteractionDefaultcommentsGet(hostGuid?: string, page?: number, pageSize?: number, hot?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsInteractionDefaultcommentsGet(hostGuid, page, pageSize, hot, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取更多折叠的评论
     * @param {number} [commentId] 
     * @param {string} [hostGuid] 文章或者动态的guid
     * @param {number} [pageIndex] 
     * @param {number} [pageSize] 
     * @param {number} [hasCommentId] 当前已显示的首个评论
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsInteractionMorecommentsGet(commentId?: number, hostGuid?: string, pageIndex?: number, pageSize?: number, hasCommentId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSocialDynamicsComment>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsInteractionMorecommentsGet(commentId, hostGuid, pageIndex, pageSize, hasCommentId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 查询未读的新机会消息数量、互动消息和系统消息数量之和
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsUnreadmsgpromptGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultCommunityHomeUnreadMessagePromptRecord>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsUnreadmsgpromptGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 添加浏览相关日志  --进入页面请不要调用接口
     * @param {SocialDynamicsViewLogModel} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SocialdynamicsApi
     */
    public async apiSocialdynamicsViewlogaddPost(body?: SocialDynamicsViewLogModel, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBoolean>> {
        return SocialdynamicsApiFp(this.configuration).apiSocialdynamicsViewlogaddPost(body, options).then((request) => request(this.axios, this.basePath));
    }
}
