/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { MeetingPositionDeliverParameter } from '../models';
import { RestfulResultApiResultModelString } from '../models';
import { RestfulResultListMeetingModel } from '../models';
import { RestfulResultListSchoolO2OKeywordModel } from '../models';
import { RestfulResultMeetingDetailDto } from '../models';
import { RestfulResultMeetingEnterpriseDto } from '../models';
import { RestfulResultO2OPositionEntry } from '../models';
import { RestfulResultObject } from '../models';
import { RestfulResultPagedListMeetingEnterpriseListItem } from '../models';
import { RestfulResultSchoolO2OPositionStatisticsOutput } from '../models';
/**
 * MeetingO2OApi - axios parameter creator
 * @export
 */
export const MeetingO2OApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 职位投递
         * @param {MeetingPositionDeliverParameter} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2ODeliverPost: async (body?: MeetingPositionDeliverParameter, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/Deliver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 招聘会企业与职位列表 含搜素
         * @param {number} [meetingID] 
         * @param {string} [keyword] 
         * @param {number} [cKeywordID] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetEnterpriseByMeetingIDGet: async (meetingID?: number, keyword?: string, cKeywordID?: number, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetEnterpriseByMeetingID`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (meetingID !== undefined) {
                localVarQueryParameter['MeetingID'] = meetingID;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (cKeywordID !== undefined) {
                localVarQueryParameter['cKeywordID'] = cKeywordID;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘会企业详情
         * @param {number} [articleID] 文章ID
         * @param {number} [enterpriseID] 企业ID
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {number} [schoolMeetingId] 校园招聘会ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetMeetingEnterpriseGet: async (articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, schoolMeetingId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetMeetingEnterprise`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            if (enterpriseID !== undefined) {
                localVarQueryParameter['enterpriseID'] = enterpriseID;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (schoolMeetingId !== undefined) {
                localVarQueryParameter['schoolMeetingId'] = schoolMeetingId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 招聘会详情
         * @param {number} [articleID] 
         * @param {ApplicationPlatform} [from] 
         * @param {number} [schoolMeetingId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetMeetingGet: async (articleID?: number, from?: ApplicationPlatform, schoolMeetingId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetMeeting`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (schoolMeetingId !== undefined) {
                localVarQueryParameter['schoolMeetingId'] = schoolMeetingId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取校园招聘会列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetMeetingListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetMeetingList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据二维码场景id获取具体招聘会的参数信息
         * @param {number} [scene] 微信二维码场景id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetMeetingParamPost: async (scene?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetMeetingParam`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (scene !== undefined) {
                localVarQueryParameter['scene'] = scene;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 校园职位详情
         * @param {number} [positionID] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetPositionGet: async (positionID?: number, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetPosition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (positionID !== undefined) {
                localVarQueryParameter['PositionID'] = positionID;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 统计schoolo2o信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OGetSchoolO2OPositionStatisticsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/GetSchoolO2OPositionStatistics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘会的自定义标签
         * @param {number} [meetingId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingO2OMeetingCustomizeKeywordGet: async (meetingId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/MeetingO2O/MeetingCustomizeKeyword`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (meetingId !== undefined) {
                localVarQueryParameter['meetingId'] = meetingId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * MeetingO2OApi - functional programming interface
 * @export
 */
export const MeetingO2OApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 职位投递
         * @param {MeetingPositionDeliverParameter} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2ODeliverPost(body?: MeetingPositionDeliverParameter, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultApiResultModelString>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2ODeliverPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 招聘会企业与职位列表 含搜素
         * @param {number} [meetingID] 
         * @param {string} [keyword] 
         * @param {number} [cKeywordID] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetEnterpriseByMeetingIDGet(meetingID?: number, keyword?: string, cKeywordID?: number, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetEnterpriseByMeetingIDGet(meetingID, keyword, cKeywordID, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘会企业详情
         * @param {number} [articleID] 文章ID
         * @param {number} [enterpriseID] 企业ID
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {number} [schoolMeetingId] 校园招聘会ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingEnterpriseGet(articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, schoolMeetingId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultMeetingEnterpriseDto>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetMeetingEnterpriseGet(articleID, enterpriseID, districtId, from, schoolMeetingId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 招聘会详情
         * @param {number} [articleID] 
         * @param {ApplicationPlatform} [from] 
         * @param {number} [schoolMeetingId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingGet(articleID?: number, from?: ApplicationPlatform, schoolMeetingId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultMeetingDetailDto>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetMeetingGet(articleID, from, schoolMeetingId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取校园招聘会列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListMeetingModel>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetMeetingListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据二维码场景id获取具体招聘会的参数信息
         * @param {number} [scene] 微信二维码场景id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingParamPost(scene?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultObject>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetMeetingParamPost(scene, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 校园职位详情
         * @param {number} [positionID] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetPositionGet(positionID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultO2OPositionEntry>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetPositionGet(positionID, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 统计schoolo2o信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetSchoolO2OPositionStatisticsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSchoolO2OPositionStatisticsOutput>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OGetSchoolO2OPositionStatisticsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘会的自定义标签
         * @param {number} [meetingId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OMeetingCustomizeKeywordGet(meetingId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListSchoolO2OKeywordModel>>> {
            const localVarAxiosArgs = await MeetingO2OApiAxiosParamCreator(configuration).apiMeetingO2OMeetingCustomizeKeywordGet(meetingId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * MeetingO2OApi - factory interface
 * @export
 */
export const MeetingO2OApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 职位投递
         * @param {MeetingPositionDeliverParameter} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2ODeliverPost(body?: MeetingPositionDeliverParameter, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultApiResultModelString>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2ODeliverPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 招聘会企业与职位列表 含搜素
         * @param {number} [meetingID] 
         * @param {string} [keyword] 
         * @param {number} [cKeywordID] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetEnterpriseByMeetingIDGet(meetingID?: number, keyword?: string, cKeywordID?: number, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetEnterpriseByMeetingIDGet(meetingID, keyword, cKeywordID, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘会企业详情
         * @param {number} [articleID] 文章ID
         * @param {number} [enterpriseID] 企业ID
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {number} [schoolMeetingId] 校园招聘会ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingEnterpriseGet(articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, schoolMeetingId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultMeetingEnterpriseDto>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetMeetingEnterpriseGet(articleID, enterpriseID, districtId, from, schoolMeetingId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 招聘会详情
         * @param {number} [articleID] 
         * @param {ApplicationPlatform} [from] 
         * @param {number} [schoolMeetingId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingGet(articleID?: number, from?: ApplicationPlatform, schoolMeetingId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultMeetingDetailDto>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetMeetingGet(articleID, from, schoolMeetingId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取校园招聘会列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListMeetingModel>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetMeetingListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据二维码场景id获取具体招聘会的参数信息
         * @param {number} [scene] 微信二维码场景id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetMeetingParamPost(scene?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultObject>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetMeetingParamPost(scene, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 校园职位详情
         * @param {number} [positionID] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetPositionGet(positionID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultO2OPositionEntry>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetPositionGet(positionID, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 统计schoolo2o信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OGetSchoolO2OPositionStatisticsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSchoolO2OPositionStatisticsOutput>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OGetSchoolO2OPositionStatisticsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘会的自定义标签
         * @param {number} [meetingId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingO2OMeetingCustomizeKeywordGet(meetingId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListSchoolO2OKeywordModel>> {
            return MeetingO2OApiFp(configuration).apiMeetingO2OMeetingCustomizeKeywordGet(meetingId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * MeetingO2OApi - object-oriented interface
 * @export
 * @class MeetingO2OApi
 * @extends {BaseAPI}
 */
export class MeetingO2OApi extends BaseAPI {
    /**
     * 
     * @summary 职位投递
     * @param {MeetingPositionDeliverParameter} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2ODeliverPost(body?: MeetingPositionDeliverParameter, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultApiResultModelString>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2ODeliverPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 招聘会企业与职位列表 含搜素
     * @param {number} [meetingID] 
     * @param {string} [keyword] 
     * @param {number} [cKeywordID] 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetEnterpriseByMeetingIDGet(meetingID?: number, keyword?: string, cKeywordID?: number, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetEnterpriseByMeetingIDGet(meetingID, keyword, cKeywordID, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘会企业详情
     * @param {number} [articleID] 文章ID
     * @param {number} [enterpriseID] 企业ID
     * @param {BussDistrict} [districtId] 地市
     * @param {ApplicationPlatform} [from] 平台来源
     * @param {number} [schoolMeetingId] 校园招聘会ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetMeetingEnterpriseGet(articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, schoolMeetingId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultMeetingEnterpriseDto>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetMeetingEnterpriseGet(articleID, enterpriseID, districtId, from, schoolMeetingId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 招聘会详情
     * @param {number} [articleID] 
     * @param {ApplicationPlatform} [from] 
     * @param {number} [schoolMeetingId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetMeetingGet(articleID?: number, from?: ApplicationPlatform, schoolMeetingId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultMeetingDetailDto>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetMeetingGet(articleID, from, schoolMeetingId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取校园招聘会列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetMeetingListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListMeetingModel>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetMeetingListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据二维码场景id获取具体招聘会的参数信息
     * @param {number} [scene] 微信二维码场景id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetMeetingParamPost(scene?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultObject>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetMeetingParamPost(scene, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 校园职位详情
     * @param {number} [positionID] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetPositionGet(positionID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultO2OPositionEntry>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetPositionGet(positionID, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 统计schoolo2o信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OGetSchoolO2OPositionStatisticsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSchoolO2OPositionStatisticsOutput>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OGetSchoolO2OPositionStatisticsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘会的自定义标签
     * @param {number} [meetingId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingO2OApi
     */
    public async apiMeetingO2OMeetingCustomizeKeywordGet(meetingId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListSchoolO2OKeywordModel>> {
        return MeetingO2OApiFp(this.configuration).apiMeetingO2OMeetingCustomizeKeywordGet(meetingId, options).then((request) => request(this.axios, this.basePath));
    }
}
