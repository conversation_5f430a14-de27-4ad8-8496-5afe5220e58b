/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RestfulResultHotPostionRankResultDto } from '../models';
import { RestfulResultSalarySearchResultSqlEntity } from '../models';
/**
 * SalaryReportApi - axios parameter creator
 * @export
 */
export const SalaryReportApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 热门职位竞争排行榜
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSalaryReportHotPostionRankGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/SalaryReport/HotPostionRank`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 薪酬查询
         * @param {Date} [from] 
         * @param {Date} [to] 
         * @param {number} [positionType] 
         * @param {number} [workPlace] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiSalaryReportSalarySearchGet: async (from?: Date, to?: Date, positionType?: number, workPlace?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/SalaryReport/SalarySearch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = (from as any instanceof Date) ?
                    (from as any).toISOString() :
                    from;
            }

            if (to !== undefined) {
                localVarQueryParameter['to'] = (to as any instanceof Date) ?
                    (to as any).toISOString() :
                    to;
            }

            if (positionType !== undefined) {
                localVarQueryParameter['positionType'] = positionType;
            }

            if (workPlace !== undefined) {
                localVarQueryParameter['workPlace'] = workPlace;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * SalaryReportApi - functional programming interface
 * @export
 */
export const SalaryReportApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 热门职位竞争排行榜
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSalaryReportHotPostionRankGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultHotPostionRankResultDto>>> {
            const localVarAxiosArgs = await SalaryReportApiAxiosParamCreator(configuration).apiSalaryReportHotPostionRankGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 薪酬查询
         * @param {Date} [from] 
         * @param {Date} [to] 
         * @param {number} [positionType] 
         * @param {number} [workPlace] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSalaryReportSalarySearchGet(from?: Date, to?: Date, positionType?: number, workPlace?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSalarySearchResultSqlEntity>>> {
            const localVarAxiosArgs = await SalaryReportApiAxiosParamCreator(configuration).apiSalaryReportSalarySearchGet(from, to, positionType, workPlace, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * SalaryReportApi - factory interface
 * @export
 */
export const SalaryReportApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 热门职位竞争排行榜
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSalaryReportHotPostionRankGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultHotPostionRankResultDto>> {
            return SalaryReportApiFp(configuration).apiSalaryReportHotPostionRankGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 薪酬查询
         * @param {Date} [from] 
         * @param {Date} [to] 
         * @param {number} [positionType] 
         * @param {number} [workPlace] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiSalaryReportSalarySearchGet(from?: Date, to?: Date, positionType?: number, workPlace?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSalarySearchResultSqlEntity>> {
            return SalaryReportApiFp(configuration).apiSalaryReportSalarySearchGet(from, to, positionType, workPlace, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * SalaryReportApi - object-oriented interface
 * @export
 * @class SalaryReportApi
 * @extends {BaseAPI}
 */
export class SalaryReportApi extends BaseAPI {
    /**
     * 
     * @summary 热门职位竞争排行榜
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SalaryReportApi
     */
    public async apiSalaryReportHotPostionRankGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultHotPostionRankResultDto>> {
        return SalaryReportApiFp(this.configuration).apiSalaryReportHotPostionRankGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 薪酬查询
     * @param {Date} [from] 
     * @param {Date} [to] 
     * @param {number} [positionType] 
     * @param {number} [workPlace] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof SalaryReportApi
     */
    public async apiSalaryReportSalarySearchGet(from?: Date, to?: Date, positionType?: number, workPlace?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSalarySearchResultSqlEntity>> {
        return SalaryReportApiFp(this.configuration).apiSalaryReportSalarySearchGet(from, to, positionType, workPlace, options).then((request) => request(this.axios, this.basePath));
    }
}
