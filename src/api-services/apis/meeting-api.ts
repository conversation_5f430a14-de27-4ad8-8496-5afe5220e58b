/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { MeetingDisplayTerms } from '../models';
import { RestfulResultListMeetingShowroomAmount } from '../models';
import { RestfulResultListNoticeDto } from '../models';
import { RestfulResultMeetingDetailDto } from '../models';
import { RestfulResultMeetingEnterpriseDto } from '../models';
import { RestfulResultMeetingEnterpriseListResult } from '../models';
import { RestfulResultPagedListMeetingEnterpriseListItem } from '../models';
import { RestfulResultPagedListMeetingListItemDto } from '../models';
import { RestfulResultPagedListO2OMeetingListItemDto } from '../models';
/**
 * MeetingApi - axios parameter creator
 * @export
 */
export const MeetingApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取现场招聘会企业列表
         * @param {number} [articleID] 招聘会文章id
         * @param {string} [keyword] 企业名称关键字
         * @param {BussDistrict} [districtID] 地市id
         * @param {ApplicationPlatform} [from] 来源平台
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetEnterpriseListGet: async (articleID?: number, keyword?: string, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/GetEnterpriseList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (districtID !== undefined) {
                localVarQueryParameter['districtID'] = districtID;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘会详细信息
         * @param {number} [articleID] 招聘会文章ID
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetMeetingBaseInfoGet: async (articleID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/GetMeetingBaseInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取招聘会企业详情
         * @param {number} [articleID] 文章ID
         * @param {number} [enterpriseID] 企业ID
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetMeetingEnterpriseGet: async (articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/GetMeetingEnterprise`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            if (enterpriseID !== undefined) {
                localVarQueryParameter['enterpriseID'] = enterpriseID;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取现场招聘会列表
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会类型
         * @param {BussDistrict} [districtID] 地市id
         * @param {ApplicationPlatform} [from] 来源平台
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetMeetingListGet: async (meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/GetMeetingList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (meetingDisplayTerms !== undefined) {
                localVarQueryParameter['meetingDisplayTerms'] = meetingDisplayTerms;
            }

            if (districtID !== undefined) {
                localVarQueryParameter['districtID'] = districtID;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取现场招聘会通知信息
         * @param {BussDistrict} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetNoticeGet: async (districtId?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/GetNotice`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取校园招聘会列表
         * @param {number} page 页码
         * @param {number} pageSize 每页记录条数
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 搜索招聘会类型
         * @param {BussDistrict} [districtID] 站点
         * @param {string} [keyWord] 搜索关键词
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetSchoolO2OMeetingListGet: async (page: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, keyWord?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'page' is not null or undefined
            if (page === null || page === undefined) {
                throw new RequiredError('page','Required parameter page was null or undefined when calling apiMeetingGetSchoolO2OMeetingListGet.');
            }
            // verify required parameter 'pageSize' is not null or undefined
            if (pageSize === null || pageSize === undefined) {
                throw new RequiredError('pageSize','Required parameter pageSize was null or undefined when calling apiMeetingGetSchoolO2OMeetingListGet.');
            }
            const localVarPath = `/api/Meeting/GetSchoolO2OMeetingList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (meetingDisplayTerms !== undefined) {
                localVarQueryParameter['MeetingDisplayTerms'] = meetingDisplayTerms;
            }

            if (districtID !== undefined) {
                localVarQueryParameter['DistrictID'] = districtID;
            }

            if (keyWord !== undefined) {
                localVarQueryParameter['KeyWord'] = keyWord;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 全区 各大展厅职位数、企业数统计
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingGetShowroomStatisticsGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/GetShowroomStatistics`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 【全区】企业搜索
         * @param {string} [keyword] 企业/职位 关键词
         * @param {number} [industryId] 行业ID
         * @param {string} [showroom] 展厅ID
         * @param {number} [booth] 展位id
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {boolean} [expanding] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingSearchEnterpriseGet: async (keyword?: string, industryId?: number, showroom?: string, booth?: number, page?: number, pageSize?: number, expanding?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/SearchEnterprise`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['Keyword'] = keyword;
            }

            if (industryId !== undefined) {
                localVarQueryParameter['IndustryId'] = industryId;
            }

            if (showroom !== undefined) {
                localVarQueryParameter['Showroom'] = showroom;
            }

            if (booth !== undefined) {
                localVarQueryParameter['Booth'] = booth;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            if (expanding !== undefined) {
                localVarQueryParameter['Expanding'] = expanding;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 现场招聘会搜索
         * @param {number} [articleID] 文章ID
         * @param {string} [keyword] 企业/职位名称关键词
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiMeetingSearchGet: async (articleID?: number, keyword?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Meeting/Search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (articleID !== undefined) {
                localVarQueryParameter['articleID'] = articleID;
            }

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * MeetingApi - functional programming interface
 * @export
 */
export const MeetingApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取现场招聘会企业列表
         * @param {number} [articleID] 招聘会文章id
         * @param {string} [keyword] 企业名称关键字
         * @param {BussDistrict} [districtID] 地市id
         * @param {ApplicationPlatform} [from] 来源平台
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetEnterpriseListGet(articleID?: number, keyword?: string, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetEnterpriseListGet(articleID, keyword, districtID, from, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘会详细信息
         * @param {number} [articleID] 招聘会文章ID
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetMeetingBaseInfoGet(articleID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultMeetingDetailDto>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetMeetingBaseInfoGet(articleID, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取招聘会企业详情
         * @param {number} [articleID] 文章ID
         * @param {number} [enterpriseID] 企业ID
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetMeetingEnterpriseGet(articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultMeetingEnterpriseDto>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetMeetingEnterpriseGet(articleID, enterpriseID, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取现场招聘会列表
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会类型
         * @param {BussDistrict} [districtID] 地市id
         * @param {ApplicationPlatform} [from] 来源平台
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetMeetingListGet(meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListMeetingListItemDto>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetMeetingListGet(meetingDisplayTerms, districtID, from, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取现场招聘会通知信息
         * @param {BussDistrict} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetNoticeGet(districtId?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListNoticeDto>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetNoticeGet(districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取校园招聘会列表
         * @param {number} page 页码
         * @param {number} pageSize 每页记录条数
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 搜索招聘会类型
         * @param {BussDistrict} [districtID] 站点
         * @param {string} [keyWord] 搜索关键词
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetSchoolO2OMeetingListGet(page: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, keyWord?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListO2OMeetingListItemDto>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetSchoolO2OMeetingListGet(page, pageSize, meetingDisplayTerms, districtID, keyWord, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 全区 各大展厅职位数、企业数统计
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetShowroomStatisticsGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListMeetingShowroomAmount>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingGetShowroomStatisticsGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 【全区】企业搜索
         * @param {string} [keyword] 企业/职位 关键词
         * @param {number} [industryId] 行业ID
         * @param {string} [showroom] 展厅ID
         * @param {number} [booth] 展位id
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {boolean} [expanding] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingSearchEnterpriseGet(keyword?: string, industryId?: number, showroom?: string, booth?: number, page?: number, pageSize?: number, expanding?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultMeetingEnterpriseListResult>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingSearchEnterpriseGet(keyword, industryId, showroom, booth, page, pageSize, expanding, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 现场招聘会搜索
         * @param {number} [articleID] 文章ID
         * @param {string} [keyword] 企业/职位名称关键词
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingSearchGet(articleID?: number, keyword?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>>> {
            const localVarAxiosArgs = await MeetingApiAxiosParamCreator(configuration).apiMeetingSearchGet(articleID, keyword, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * MeetingApi - factory interface
 * @export
 */
export const MeetingApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取现场招聘会企业列表
         * @param {number} [articleID] 招聘会文章id
         * @param {string} [keyword] 企业名称关键字
         * @param {BussDistrict} [districtID] 地市id
         * @param {ApplicationPlatform} [from] 来源平台
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetEnterpriseListGet(articleID?: number, keyword?: string, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>> {
            return MeetingApiFp(configuration).apiMeetingGetEnterpriseListGet(articleID, keyword, districtID, from, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘会详细信息
         * @param {number} [articleID] 招聘会文章ID
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetMeetingBaseInfoGet(articleID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultMeetingDetailDto>> {
            return MeetingApiFp(configuration).apiMeetingGetMeetingBaseInfoGet(articleID, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取招聘会企业详情
         * @param {number} [articleID] 文章ID
         * @param {number} [enterpriseID] 企业ID
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetMeetingEnterpriseGet(articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultMeetingEnterpriseDto>> {
            return MeetingApiFp(configuration).apiMeetingGetMeetingEnterpriseGet(articleID, enterpriseID, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取现场招聘会列表
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会类型
         * @param {BussDistrict} [districtID] 地市id
         * @param {ApplicationPlatform} [from] 来源平台
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetMeetingListGet(meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListMeetingListItemDto>> {
            return MeetingApiFp(configuration).apiMeetingGetMeetingListGet(meetingDisplayTerms, districtID, from, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取现场招聘会通知信息
         * @param {BussDistrict} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetNoticeGet(districtId?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListNoticeDto>> {
            return MeetingApiFp(configuration).apiMeetingGetNoticeGet(districtId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取校园招聘会列表
         * @param {number} page 页码
         * @param {number} pageSize 每页记录条数
         * @param {MeetingDisplayTerms} [meetingDisplayTerms] 搜索招聘会类型
         * @param {BussDistrict} [districtID] 站点
         * @param {string} [keyWord] 搜索关键词
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetSchoolO2OMeetingListGet(page: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, keyWord?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListO2OMeetingListItemDto>> {
            return MeetingApiFp(configuration).apiMeetingGetSchoolO2OMeetingListGet(page, pageSize, meetingDisplayTerms, districtID, keyWord, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 全区 各大展厅职位数、企业数统计
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingGetShowroomStatisticsGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListMeetingShowroomAmount>> {
            return MeetingApiFp(configuration).apiMeetingGetShowroomStatisticsGet(districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 【全区】企业搜索
         * @param {string} [keyword] 企业/职位 关键词
         * @param {number} [industryId] 行业ID
         * @param {string} [showroom] 展厅ID
         * @param {number} [booth] 展位id
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {boolean} [expanding] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingSearchEnterpriseGet(keyword?: string, industryId?: number, showroom?: string, booth?: number, page?: number, pageSize?: number, expanding?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultMeetingEnterpriseListResult>> {
            return MeetingApiFp(configuration).apiMeetingSearchEnterpriseGet(keyword, industryId, showroom, booth, page, pageSize, expanding, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 现场招聘会搜索
         * @param {number} [articleID] 文章ID
         * @param {string} [keyword] 企业/职位名称关键词
         * @param {BussDistrict} [districtId] 地市
         * @param {ApplicationPlatform} [from] 平台来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiMeetingSearchGet(articleID?: number, keyword?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>> {
            return MeetingApiFp(configuration).apiMeetingSearchGet(articleID, keyword, districtId, from, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * MeetingApi - object-oriented interface
 * @export
 * @class MeetingApi
 * @extends {BaseAPI}
 */
export class MeetingApi extends BaseAPI {
    /**
     * 
     * @summary 获取现场招聘会企业列表
     * @param {number} [articleID] 招聘会文章id
     * @param {string} [keyword] 企业名称关键字
     * @param {BussDistrict} [districtID] 地市id
     * @param {ApplicationPlatform} [from] 来源平台
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetEnterpriseListGet(articleID?: number, keyword?: string, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>> {
        return MeetingApiFp(this.configuration).apiMeetingGetEnterpriseListGet(articleID, keyword, districtID, from, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘会详细信息
     * @param {number} [articleID] 招聘会文章ID
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetMeetingBaseInfoGet(articleID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultMeetingDetailDto>> {
        return MeetingApiFp(this.configuration).apiMeetingGetMeetingBaseInfoGet(articleID, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取招聘会企业详情
     * @param {number} [articleID] 文章ID
     * @param {number} [enterpriseID] 企业ID
     * @param {BussDistrict} [districtId] 地市
     * @param {ApplicationPlatform} [from] 平台来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetMeetingEnterpriseGet(articleID?: number, enterpriseID?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultMeetingEnterpriseDto>> {
        return MeetingApiFp(this.configuration).apiMeetingGetMeetingEnterpriseGet(articleID, enterpriseID, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取现场招聘会列表
     * @param {MeetingDisplayTerms} [meetingDisplayTerms] 招聘会类型
     * @param {BussDistrict} [districtID] 地市id
     * @param {ApplicationPlatform} [from] 来源平台
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetMeetingListGet(meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListMeetingListItemDto>> {
        return MeetingApiFp(this.configuration).apiMeetingGetMeetingListGet(meetingDisplayTerms, districtID, from, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取现场招聘会通知信息
     * @param {BussDistrict} [districtId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetNoticeGet(districtId?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListNoticeDto>> {
        return MeetingApiFp(this.configuration).apiMeetingGetNoticeGet(districtId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取校园招聘会列表
     * @param {number} page 页码
     * @param {number} pageSize 每页记录条数
     * @param {MeetingDisplayTerms} [meetingDisplayTerms] 搜索招聘会类型
     * @param {BussDistrict} [districtID] 站点
     * @param {string} [keyWord] 搜索关键词
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetSchoolO2OMeetingListGet(page: number, pageSize: number, meetingDisplayTerms?: MeetingDisplayTerms, districtID?: BussDistrict, keyWord?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListO2OMeetingListItemDto>> {
        return MeetingApiFp(this.configuration).apiMeetingGetSchoolO2OMeetingListGet(page, pageSize, meetingDisplayTerms, districtID, keyWord, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 全区 各大展厅职位数、企业数统计
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingGetShowroomStatisticsGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListMeetingShowroomAmount>> {
        return MeetingApiFp(this.configuration).apiMeetingGetShowroomStatisticsGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 【全区】企业搜索
     * @param {string} [keyword] 企业/职位 关键词
     * @param {number} [industryId] 行业ID
     * @param {string} [showroom] 展厅ID
     * @param {number} [booth] 展位id
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {boolean} [expanding] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingSearchEnterpriseGet(keyword?: string, industryId?: number, showroom?: string, booth?: number, page?: number, pageSize?: number, expanding?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultMeetingEnterpriseListResult>> {
        return MeetingApiFp(this.configuration).apiMeetingSearchEnterpriseGet(keyword, industryId, showroom, booth, page, pageSize, expanding, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 现场招聘会搜索
     * @param {number} [articleID] 文章ID
     * @param {string} [keyword] 企业/职位名称关键词
     * @param {BussDistrict} [districtId] 地市
     * @param {ApplicationPlatform} [from] 平台来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof MeetingApi
     */
    public async apiMeetingSearchGet(articleID?: number, keyword?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListMeetingEnterpriseListItem>> {
        return MeetingApiFp(this.configuration).apiMeetingSearchGet(articleID, keyword, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
}
