/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { RestfulResultString } from '../models';
import { RestfulResultWondersYunTokenResponse } from '../models';
/**
 * LiuzhouwondersyunApi - axios parameter creator
 * @export
 */
export const LiuzhouwondersyunApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 请求用户授权 Token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLiuzhouwondersyunAuthorizeGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/liuzhouwondersyun/authorize`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取Access Token(已弃用，仅限测试)
         * @param {string} code 用于调用 access_token的代码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLiuzhouwondersyunTokenCodeGet: async (code: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'code' is not null or undefined
            if (code === null || code === undefined) {
                throw new RequiredError('code','Required parameter code was null or undefined when calling apiLiuzhouwondersyunTokenCodeGet.');
            }
            const localVarPath = `/api/liuzhouwondersyun/token/{code}`
                .replace(`{${"code"}}`, encodeURIComponent(String(code)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取当前登录用户的手机号码
         * @param {string} code 用于调用 access_token的代码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLiuzhouwondersyunUseridentityCodeGet: async (code: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'code' is not null or undefined
            if (code === null || code === undefined) {
                throw new RequiredError('code','Required parameter code was null or undefined when calling apiLiuzhouwondersyunUseridentityCodeGet.');
            }
            const localVarPath = `/api/liuzhouwondersyun/useridentity/{code}`
                .replace(`{${"code"}}`, encodeURIComponent(String(code)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * LiuzhouwondersyunApi - functional programming interface
 * @export
 */
export const LiuzhouwondersyunApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 请求用户授权 Token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLiuzhouwondersyunAuthorizeGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultString>>> {
            const localVarAxiosArgs = await LiuzhouwondersyunApiAxiosParamCreator(configuration).apiLiuzhouwondersyunAuthorizeGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取Access Token(已弃用，仅限测试)
         * @param {string} code 用于调用 access_token的代码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLiuzhouwondersyunTokenCodeGet(code: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultWondersYunTokenResponse>>> {
            const localVarAxiosArgs = await LiuzhouwondersyunApiAxiosParamCreator(configuration).apiLiuzhouwondersyunTokenCodeGet(code, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取当前登录用户的手机号码
         * @param {string} code 用于调用 access_token的代码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLiuzhouwondersyunUseridentityCodeGet(code: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultString>>> {
            const localVarAxiosArgs = await LiuzhouwondersyunApiAxiosParamCreator(configuration).apiLiuzhouwondersyunUseridentityCodeGet(code, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * LiuzhouwondersyunApi - factory interface
 * @export
 */
export const LiuzhouwondersyunApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 请求用户授权 Token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLiuzhouwondersyunAuthorizeGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultString>> {
            return LiuzhouwondersyunApiFp(configuration).apiLiuzhouwondersyunAuthorizeGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取Access Token(已弃用，仅限测试)
         * @param {string} code 用于调用 access_token的代码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLiuzhouwondersyunTokenCodeGet(code: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultWondersYunTokenResponse>> {
            return LiuzhouwondersyunApiFp(configuration).apiLiuzhouwondersyunTokenCodeGet(code, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取当前登录用户的手机号码
         * @param {string} code 用于调用 access_token的代码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLiuzhouwondersyunUseridentityCodeGet(code: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultString>> {
            return LiuzhouwondersyunApiFp(configuration).apiLiuzhouwondersyunUseridentityCodeGet(code, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * LiuzhouwondersyunApi - object-oriented interface
 * @export
 * @class LiuzhouwondersyunApi
 * @extends {BaseAPI}
 */
export class LiuzhouwondersyunApi extends BaseAPI {
    /**
     * 
     * @summary 请求用户授权 Token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LiuzhouwondersyunApi
     */
    public async apiLiuzhouwondersyunAuthorizeGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultString>> {
        return LiuzhouwondersyunApiFp(this.configuration).apiLiuzhouwondersyunAuthorizeGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取Access Token(已弃用，仅限测试)
     * @param {string} code 用于调用 access_token的代码
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LiuzhouwondersyunApi
     */
    public async apiLiuzhouwondersyunTokenCodeGet(code: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultWondersYunTokenResponse>> {
        return LiuzhouwondersyunApiFp(this.configuration).apiLiuzhouwondersyunTokenCodeGet(code, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取当前登录用户的手机号码
     * @param {string} code 用于调用 access_token的代码
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LiuzhouwondersyunApi
     */
    public async apiLiuzhouwondersyunUseridentityCodeGet(code: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultString>> {
        return LiuzhouwondersyunApiFp(this.configuration).apiLiuzhouwondersyunUseridentityCodeGet(code, options).then((request) => request(this.axios, this.basePath));
    }
}
