/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { EnterpriseClickFrom } from '../models';
import { PositionRequest } from '../models';
import { RestfulResultBoolean } from '../models';
import { RestfulResultEnterpriseContactDto } from '../models';
import { RestfulResultEnterpriseDisplayDto } from '../models';
import { RestfulResultEnterprisePositionListDto } from '../models';
import { RestfulResultEnterpriseVisualMediaModel } from '../models';
import { RestfulResultListEnterpriseVideoOutput } from '../models';
import { RestfulResultListPositionRecruitStaticModel } from '../models';
import { RestfulResultNullableGuid } from '../models';
import { RestfulResultPagedListIndexEnterpriseItemDto } from '../models';
import { RestfulResultPagedListIndexEnterpriseItemDtoOriginal } from '../models';
import { RestfulResultQccECIV4Model } from '../models';
/**
 * EnterpriseApi - axios parameter creator
 * @export
 */
export const EnterpriseApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 企业id转换guid
         * @param {number} [enterpriseId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseConvertedGet: async (enterpriseId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/Converted`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (enterpriseId !== undefined) {
                localVarQueryParameter['enterpriseId'] = enterpriseId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业详情
         * @param {string} [guid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseDetailGet: async (guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/Detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (clickFrom !== undefined) {
                localVarQueryParameter['clickFrom'] = clickFrom;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取公司形象媒介
         * @param {string} [enterpriseGuid] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseGetEnterpriseVisualMediaGet: async (enterpriseGuid?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/GetEnterpriseVisualMedia`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (enterpriseGuid !== undefined) {
                localVarQueryParameter['enterpriseGuid'] = enterpriseGuid;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业电话号码
         * @param {string} [signature] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterprisePhoneImageGet: async (signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/PhoneImage`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (signature !== undefined) {
                localVarQueryParameter['signature'] = signature;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业电话号码 白色字
         * @param {string} [signature] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterprisePhoneImageWhiteGet: async (signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/PhoneImageWhite`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (signature !== undefined) {
                localVarQueryParameter['signature'] = signature;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业职位列表
         * @param {string} [guid] 
         * @param {number} [positionType] 按职位类型筛选
         * @param {number} [cityID] 按城市ID筛选
         * @param {number} [paypackage] 按薪资筛选
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterprisePositionListGet: async (guid?: string, positionType?: number, cityID?: number, paypackage?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/PositionList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (positionType !== undefined) {
                localVarQueryParameter['positionType'] = positionType;
            }

            if (cityID !== undefined) {
                localVarQueryParameter['cityID'] = cityID;
            }

            if (paypackage !== undefined) {
                localVarQueryParameter['paypackage'] = paypackage;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业工商管理信息：企查查
         * @param {string} [guid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseQccGet: async (guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/Qcc`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查看企业联系方式
         * @param {string} [enterpriseGuid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseRealContactGet: async (enterpriseGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/RealContact`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (enterpriseGuid !== undefined) {
                localVarQueryParameter['enterpriseGuid'] = enterpriseGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 招聘简章页面的招聘数据
         * @param {string} [guid] 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseRecruitStaticGet: async (guid?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/RecruitStatic`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseSearchForMyPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/SearchForMy`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业搜索返回原始字段
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseSearchOriginalPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/SearchOriginal`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 企业搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseSearchPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/Search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新工商管理信息
         * @param {number} [enterpriseId] 
         * @param {string} [token] 访问次接口需要用令牌
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseUpdateQccGet: async (enterpriseId?: number, token?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Enterprise/UpdateQcc`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (enterpriseId !== undefined) {
                localVarQueryParameter['enterpriseId'] = enterpriseId;
            }

            if (token !== undefined) {
                localVarQueryParameter['token'] = token;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据企业GUID查询企业的审核通过的形象视频清单
         * @param {string} enterpriseGuid 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiEnterpriseVideosEnterpriseGuidGet: async (enterpriseGuid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'enterpriseGuid' is not null or undefined
            if (enterpriseGuid === null || enterpriseGuid === undefined) {
                throw new RequiredError('enterpriseGuid','Required parameter enterpriseGuid was null or undefined when calling apiEnterpriseVideosEnterpriseGuidGet.');
            }
            const localVarPath = `/api/Enterprise/videos/{enterpriseGuid}`
                .replace(`{${"enterpriseGuid"}}`, encodeURIComponent(String(enterpriseGuid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * EnterpriseApi - functional programming interface
 * @export
 */
export const EnterpriseApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 企业id转换guid
         * @param {number} [enterpriseId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseConvertedGet(enterpriseId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultNullableGuid>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseConvertedGet(enterpriseId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业详情
         * @param {string} [guid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterpriseDisplayDto>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseDetailGet(guid, districtId, from, clickFrom, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取公司形象媒介
         * @param {string} [enterpriseGuid] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseGetEnterpriseVisualMediaGet(enterpriseGuid?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterpriseVisualMediaModel>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseGetEnterpriseVisualMediaGet(enterpriseGuid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业电话号码
         * @param {string} [signature] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterprisePhoneImageGet(signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterprisePhoneImageGet(signature, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业电话号码 白色字
         * @param {string} [signature] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterprisePhoneImageWhiteGet(signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterprisePhoneImageWhiteGet(signature, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业职位列表
         * @param {string} [guid] 
         * @param {number} [positionType] 按职位类型筛选
         * @param {number} [cityID] 按城市ID筛选
         * @param {number} [paypackage] 按薪资筛选
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterprisePositionListGet(guid?: string, positionType?: number, cityID?: number, paypackage?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterprisePositionListDto>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterprisePositionListGet(guid, positionType, cityID, paypackage, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业工商管理信息：企查查
         * @param {string} [guid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseQccGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultQccECIV4Model>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseQccGet(guid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 查看企业联系方式
         * @param {string} [enterpriseGuid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseRealContactGet(enterpriseGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultEnterpriseContactDto>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseRealContactGet(enterpriseGuid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 招聘简章页面的招聘数据
         * @param {string} [guid] 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseRecruitStaticGet(guid?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListPositionRecruitStaticModel>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseRecruitStaticGet(guid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseSearchForMyPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDto>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseSearchForMyPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业搜索返回原始字段
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseSearchOriginalPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDtoOriginal>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseSearchOriginalPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 企业搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDto>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseSearchPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新工商管理信息
         * @param {number} [enterpriseId] 
         * @param {string} [token] 访问次接口需要用令牌
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseUpdateQccGet(enterpriseId?: number, token?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBoolean>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseUpdateQccGet(enterpriseId, token, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据企业GUID查询企业的审核通过的形象视频清单
         * @param {string} enterpriseGuid 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseVideosEnterpriseGuidGet(enterpriseGuid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListEnterpriseVideoOutput>>> {
            const localVarAxiosArgs = await EnterpriseApiAxiosParamCreator(configuration).apiEnterpriseVideosEnterpriseGuidGet(enterpriseGuid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * EnterpriseApi - factory interface
 * @export
 */
export const EnterpriseApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 企业id转换guid
         * @param {number} [enterpriseId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseConvertedGet(enterpriseId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultNullableGuid>> {
            return EnterpriseApiFp(configuration).apiEnterpriseConvertedGet(enterpriseId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业详情
         * @param {string} [guid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterpriseDisplayDto>> {
            return EnterpriseApiFp(configuration).apiEnterpriseDetailGet(guid, districtId, from, clickFrom, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取公司形象媒介
         * @param {string} [enterpriseGuid] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseGetEnterpriseVisualMediaGet(enterpriseGuid?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterpriseVisualMediaModel>> {
            return EnterpriseApiFp(configuration).apiEnterpriseGetEnterpriseVisualMediaGet(enterpriseGuid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业电话号码
         * @param {string} [signature] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterprisePhoneImageGet(signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnterpriseApiFp(configuration).apiEnterprisePhoneImageGet(signature, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业电话号码 白色字
         * @param {string} [signature] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterprisePhoneImageWhiteGet(signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return EnterpriseApiFp(configuration).apiEnterprisePhoneImageWhiteGet(signature, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业职位列表
         * @param {string} [guid] 
         * @param {number} [positionType] 按职位类型筛选
         * @param {number} [cityID] 按城市ID筛选
         * @param {number} [paypackage] 按薪资筛选
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterprisePositionListGet(guid?: string, positionType?: number, cityID?: number, paypackage?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterprisePositionListDto>> {
            return EnterpriseApiFp(configuration).apiEnterprisePositionListGet(guid, positionType, cityID, paypackage, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业工商管理信息：企查查
         * @param {string} [guid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseQccGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultQccECIV4Model>> {
            return EnterpriseApiFp(configuration).apiEnterpriseQccGet(guid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查看企业联系方式
         * @param {string} [enterpriseGuid] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseRealContactGet(enterpriseGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultEnterpriseContactDto>> {
            return EnterpriseApiFp(configuration).apiEnterpriseRealContactGet(enterpriseGuid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 招聘简章页面的招聘数据
         * @param {string} [guid] 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseRecruitStaticGet(guid?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListPositionRecruitStaticModel>> {
            return EnterpriseApiFp(configuration).apiEnterpriseRecruitStaticGet(guid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseSearchForMyPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDto>> {
            return EnterpriseApiFp(configuration).apiEnterpriseSearchForMyPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业搜索返回原始字段
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseSearchOriginalPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDtoOriginal>> {
            return EnterpriseApiFp(configuration).apiEnterpriseSearchOriginalPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 企业搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDto>> {
            return EnterpriseApiFp(configuration).apiEnterpriseSearchPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新工商管理信息
         * @param {number} [enterpriseId] 
         * @param {string} [token] 访问次接口需要用令牌
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseUpdateQccGet(enterpriseId?: number, token?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBoolean>> {
            return EnterpriseApiFp(configuration).apiEnterpriseUpdateQccGet(enterpriseId, token, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据企业GUID查询企业的审核通过的形象视频清单
         * @param {string} enterpriseGuid 企业GUID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiEnterpriseVideosEnterpriseGuidGet(enterpriseGuid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListEnterpriseVideoOutput>> {
            return EnterpriseApiFp(configuration).apiEnterpriseVideosEnterpriseGuidGet(enterpriseGuid, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * EnterpriseApi - object-oriented interface
 * @export
 * @class EnterpriseApi
 * @extends {BaseAPI}
 */
export class EnterpriseApi extends BaseAPI {
    /**
     * 
     * @summary 企业id转换guid
     * @param {number} [enterpriseId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseConvertedGet(enterpriseId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultNullableGuid>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseConvertedGet(enterpriseId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业详情
     * @param {string} [guid] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {EnterpriseClickFrom} [clickFrom] 点击来源  1：推荐企业
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, clickFrom?: EnterpriseClickFrom, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterpriseDisplayDto>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseDetailGet(guid, districtId, from, clickFrom, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取公司形象媒介
     * @param {string} [enterpriseGuid] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseGetEnterpriseVisualMediaGet(enterpriseGuid?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterpriseVisualMediaModel>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseGetEnterpriseVisualMediaGet(enterpriseGuid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业电话号码
     * @param {string} [signature] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterprisePhoneImageGet(signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnterpriseApiFp(this.configuration).apiEnterprisePhoneImageGet(signature, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业电话号码 白色字
     * @param {string} [signature] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterprisePhoneImageWhiteGet(signature?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return EnterpriseApiFp(this.configuration).apiEnterprisePhoneImageWhiteGet(signature, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业职位列表
     * @param {string} [guid] 
     * @param {number} [positionType] 按职位类型筛选
     * @param {number} [cityID] 按城市ID筛选
     * @param {number} [paypackage] 按薪资筛选
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterprisePositionListGet(guid?: string, positionType?: number, cityID?: number, paypackage?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterprisePositionListDto>> {
        return EnterpriseApiFp(this.configuration).apiEnterprisePositionListGet(guid, positionType, cityID, paypackage, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业工商管理信息：企查查
     * @param {string} [guid] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseQccGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultQccECIV4Model>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseQccGet(guid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 查看企业联系方式
     * @param {string} [enterpriseGuid] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseRealContactGet(enterpriseGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultEnterpriseContactDto>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseRealContactGet(enterpriseGuid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 招聘简章页面的招聘数据
     * @param {string} [guid] 企业GUID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseRecruitStaticGet(guid?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListPositionRecruitStaticModel>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseRecruitStaticGet(guid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业搜索
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseSearchForMyPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDto>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseSearchForMyPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业搜索返回原始字段
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseSearchOriginalPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDtoOriginal>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseSearchOriginalPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 企业搜索
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexEnterpriseItemDto>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseSearchPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新工商管理信息
     * @param {number} [enterpriseId] 
     * @param {string} [token] 访问次接口需要用令牌
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseUpdateQccGet(enterpriseId?: number, token?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBoolean>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseUpdateQccGet(enterpriseId, token, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据企业GUID查询企业的审核通过的形象视频清单
     * @param {string} enterpriseGuid 企业GUID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof EnterpriseApi
     */
    public async apiEnterpriseVideosEnterpriseGuidGet(enterpriseGuid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListEnterpriseVideoOutput>> {
        return EnterpriseApiFp(this.configuration).apiEnterpriseVideosEnterpriseGuidGet(enterpriseGuid, options).then((request) => request(this.axios, this.basePath));
    }
}
