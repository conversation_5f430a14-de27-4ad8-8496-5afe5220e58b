/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdType } from '../models';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { LogoPositionExposureDto } from '../models';
import { LogoPositionLogDto } from '../models';
import { RestfulResultListLogoEnterpriseListItemDto } from '../models';
import { RestfulResultLoadingADDto } from '../models';
import { RestfulResultPagedListLogoEnterpriseItemDto } from '../models';
import { RestfulResultPagedListLogoEnterpriseListItemDto } from '../models';
/**
 * LogoApi - axios parameter creator
 * @export
 */
export const LogoApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取指定类型的AppLogo(旧版appAd/AppAdList接口)
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {AdType} [adType] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoAppAdListGet: async (page?: number, pageSize?: number, districtId?: BussDistrict, adType?: AdType, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/AppAdList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (adType !== undefined) {
                localVarQueryParameter['adType'] = adType;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 点击logo
         * @param {number} [logoID] logoID
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoClickPost: async (logoID?: number, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/Click`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (logoID !== undefined) {
                localVarQueryParameter['logoID'] = logoID;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 【触屏版】触屏版对应首页非站内大横幅广告
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoCorrespondBigBannerGet: async (page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/CorrespondBigBanner`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary app load页面 弹出广告
         * @param {string} [url] 
         * @param {number} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoLoadingADGet: async (url?: string, from?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/LoadingAD`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (url !== undefined) {
                localVarQueryParameter['url'] = url;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 【触屏版】名企招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoMingQiZhaoPinGet: async (page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/MingQiZhaoPin`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 【触屏版】品牌招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoPinPaiZhaoPinGet: async (page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/PinPaiZhaoPin`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 广告Logo职位点击
         * @param {LogoPositionLogDto} [body] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoPositionClickPost: async (body?: LogoPositionLogDto, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/PositionClick`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 广告Logo职位曝光度
         * @param {LogoPositionExposureDto} [body] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoPositionExposurePost: async (body?: LogoPositionExposureDto, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/PositionExposure`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 【触屏版】热点招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoReDianZhanPinOfH5Get: async (page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/ReDianZhanPinOfH5`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 【触屏版】推荐企业
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiLogoRecommendEnterpriseGet: async (page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Logo/RecommendEnterprise`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * LogoApi - functional programming interface
 * @export
 */
export const LogoApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取指定类型的AppLogo(旧版appAd/AppAdList接口)
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {AdType} [adType] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoAppAdListGet(page?: number, pageSize?: number, districtId?: BussDistrict, adType?: AdType, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoAppAdListGet(page, pageSize, districtId, adType, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 点击logo
         * @param {number} [logoID] logoID
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoClickPost(logoID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoClickPost(logoID, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 【触屏版】触屏版对应首页非站内大横幅广告
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoCorrespondBigBannerGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoCorrespondBigBannerGet(page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary app load页面 弹出广告
         * @param {string} [url] 
         * @param {number} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoLoadingADGet(url?: string, from?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultLoadingADDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoLoadingADGet(url, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 【触屏版】名企招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoMingQiZhaoPinGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoMingQiZhaoPinGet(page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 【触屏版】品牌招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPinPaiZhaoPinGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoPinPaiZhaoPinGet(page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 广告Logo职位点击
         * @param {LogoPositionLogDto} [body] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionClickPost(body?: LogoPositionLogDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoPositionClickPost(body, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 广告Logo职位曝光度
         * @param {LogoPositionExposureDto} [body] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionExposurePost(body?: LogoPositionExposureDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoPositionExposurePost(body, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 【触屏版】热点招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoReDianZhanPinOfH5Get(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoReDianZhanPinOfH5Get(page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 【触屏版】推荐企业
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoRecommendEnterpriseGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListLogoEnterpriseListItemDto>>> {
            const localVarAxiosArgs = await LogoApiAxiosParamCreator(configuration).apiLogoRecommendEnterpriseGet(page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * LogoApi - factory interface
 * @export
 */
export const LogoApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取指定类型的AppLogo(旧版appAd/AppAdList接口)
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {AdType} [adType] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoAppAdListGet(page?: number, pageSize?: number, districtId?: BussDistrict, adType?: AdType, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>> {
            return LogoApiFp(configuration).apiLogoAppAdListGet(page, pageSize, districtId, adType, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 点击logo
         * @param {number} [logoID] logoID
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoClickPost(logoID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return LogoApiFp(configuration).apiLogoClickPost(logoID, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 【触屏版】触屏版对应首页非站内大横幅广告
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoCorrespondBigBannerGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>> {
            return LogoApiFp(configuration).apiLogoCorrespondBigBannerGet(page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary app load页面 弹出广告
         * @param {string} [url] 
         * @param {number} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoLoadingADGet(url?: string, from?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultLoadingADDto>> {
            return LogoApiFp(configuration).apiLogoLoadingADGet(url, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 【触屏版】名企招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoMingQiZhaoPinGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>> {
            return LogoApiFp(configuration).apiLogoMingQiZhaoPinGet(page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 【触屏版】品牌招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPinPaiZhaoPinGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>> {
            return LogoApiFp(configuration).apiLogoPinPaiZhaoPinGet(page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 广告Logo职位点击
         * @param {LogoPositionLogDto} [body] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionClickPost(body?: LogoPositionLogDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return LogoApiFp(configuration).apiLogoPositionClickPost(body, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 广告Logo职位曝光度
         * @param {LogoPositionExposureDto} [body] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoPositionExposurePost(body?: LogoPositionExposureDto, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return LogoApiFp(configuration).apiLogoPositionExposurePost(body, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 【触屏版】热点招聘
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoReDianZhanPinOfH5Get(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>> {
            return LogoApiFp(configuration).apiLogoReDianZhanPinOfH5Get(page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 【触屏版】推荐企业
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 网联地址
         * @param {ApplicationPlatform} [from] 请求来源/适配平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiLogoRecommendEnterpriseGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListLogoEnterpriseListItemDto>> {
            return LogoApiFp(configuration).apiLogoRecommendEnterpriseGet(page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * LogoApi - object-oriented interface
 * @export
 * @class LogoApi
 * @extends {BaseAPI}
 */
export class LogoApi extends BaseAPI {
    /**
     * 
     * @summary 获取指定类型的AppLogo(旧版appAd/AppAdList接口)
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {BussDistrict} [districtId] 
     * @param {AdType} [adType] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoAppAdListGet(page?: number, pageSize?: number, districtId?: BussDistrict, adType?: AdType, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>> {
        return LogoApiFp(this.configuration).apiLogoAppAdListGet(page, pageSize, districtId, adType, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 点击logo
     * @param {number} [logoID] logoID
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoClickPost(logoID?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return LogoApiFp(this.configuration).apiLogoClickPost(logoID, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 【触屏版】触屏版对应首页非站内大横幅广告
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {BussDistrict} [districtId] 网联地址
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoCorrespondBigBannerGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseItemDto>> {
        return LogoApiFp(this.configuration).apiLogoCorrespondBigBannerGet(page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary app load页面 弹出广告
     * @param {string} [url] 
     * @param {number} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoLoadingADGet(url?: string, from?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultLoadingADDto>> {
        return LogoApiFp(this.configuration).apiLogoLoadingADGet(url, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 【触屏版】名企招聘
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {BussDistrict} [districtId] 网联地址
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoMingQiZhaoPinGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>> {
        return LogoApiFp(this.configuration).apiLogoMingQiZhaoPinGet(page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 【触屏版】品牌招聘
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {BussDistrict} [districtId] 网联地址
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoPinPaiZhaoPinGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>> {
        return LogoApiFp(this.configuration).apiLogoPinPaiZhaoPinGet(page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 广告Logo职位点击
     * @param {LogoPositionLogDto} [body] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoPositionClickPost(body?: LogoPositionLogDto, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return LogoApiFp(this.configuration).apiLogoPositionClickPost(body, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 广告Logo职位曝光度
     * @param {LogoPositionExposureDto} [body] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoPositionExposurePost(body?: LogoPositionExposureDto, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return LogoApiFp(this.configuration).apiLogoPositionExposurePost(body, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 【触屏版】热点招聘
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {BussDistrict} [districtId] 网联地址
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoReDianZhanPinOfH5Get(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListLogoEnterpriseListItemDto>> {
        return LogoApiFp(this.configuration).apiLogoReDianZhanPinOfH5Get(page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 【触屏版】推荐企业
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {BussDistrict} [districtId] 网联地址
     * @param {ApplicationPlatform} [from] 请求来源/适配平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof LogoApi
     */
    public async apiLogoRecommendEnterpriseGet(page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListLogoEnterpriseListItemDto>> {
        return LogoApiFp(this.configuration).apiLogoRecommendEnterpriseGet(page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
}
