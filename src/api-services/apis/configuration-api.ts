/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { BussDistrict } from '../models';
import { RestfulResultObject } from '../models';
import { RestfulResultShareConfigDto } from '../models';
/**
 * ConfigurationApi - axios parameter creator
 * @export
 */
export const ConfigurationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取微信分享配置
         * @param {string} [url] 
         * @param {string} [callback] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConfigurationShareConfigGet: async (url?: string, callback?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Configuration/ShareConfig`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (url !== undefined) {
                localVarQueryParameter['url'] = url;
            }

            if (callback !== undefined) {
                localVarQueryParameter['callback'] = callback;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {BussDistrict} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiConfigurationWlSettingGet: async (districtId?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Configuration/WlSetting`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ConfigurationApi - functional programming interface
 * @export
 */
export const ConfigurationApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取微信分享配置
         * @param {string} [url] 
         * @param {string} [callback] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationShareConfigGet(url?: string, callback?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultShareConfigDto>>> {
            const localVarAxiosArgs = await ConfigurationApiAxiosParamCreator(configuration).apiConfigurationShareConfigGet(url, callback, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @param {BussDistrict} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationWlSettingGet(districtId?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultObject>>> {
            const localVarAxiosArgs = await ConfigurationApiAxiosParamCreator(configuration).apiConfigurationWlSettingGet(districtId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * ConfigurationApi - factory interface
 * @export
 */
export const ConfigurationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取微信分享配置
         * @param {string} [url] 
         * @param {string} [callback] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationShareConfigGet(url?: string, callback?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultShareConfigDto>> {
            return ConfigurationApiFp(configuration).apiConfigurationShareConfigGet(url, callback, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {BussDistrict} [districtId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiConfigurationWlSettingGet(districtId?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultObject>> {
            return ConfigurationApiFp(configuration).apiConfigurationWlSettingGet(districtId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ConfigurationApi - object-oriented interface
 * @export
 * @class ConfigurationApi
 * @extends {BaseAPI}
 */
export class ConfigurationApi extends BaseAPI {
    /**
     * 
     * @summary 获取微信分享配置
     * @param {string} [url] 
     * @param {string} [callback] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigurationApi
     */
    public async apiConfigurationShareConfigGet(url?: string, callback?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultShareConfigDto>> {
        return ConfigurationApiFp(this.configuration).apiConfigurationShareConfigGet(url, callback, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @param {BussDistrict} [districtId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ConfigurationApi
     */
    public async apiConfigurationWlSettingGet(districtId?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultObject>> {
        return ConfigurationApiFp(this.configuration).apiConfigurationWlSettingGet(districtId, options).then((request) => request(this.axios, this.basePath));
    }
}
