/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApiResultModel } from '../models';
import { ApplicationPlatform } from '../models';
import { ArticlePushRequest } from '../models';
import { BussDistrict } from '../models';
import { EDataType } from '../models';
import { PositionClickFrom } from '../models';
import { PositionRecommendRequest } from '../models';
import { PositionRequest } from '../models';
import { RecommendUninterested } from '../models';
import { RestfulResultBaseDataOutputBoolean } from '../models';
import { RestfulResultIEnumerableDailyRecommendOutput } from '../models';
import { RestfulResultJuageShowSubscribeOutput } from '../models';
import { RestfulResultListAppPositionRecommendItemModel } from '../models';
import { RestfulResultListBlueFloorFirst } from '../models';
import { RestfulResultListCampusScreenPositionItemDto } from '../models';
import { RestfulResultListPositionInfoForDefaultOTO } from '../models';
import { RestfulResultListPositionListItem } from '../models';
import { RestfulResultListSubscribePositionInfoItemOutput } from '../models';
import { RestfulResultPagedListIndexArticlePushItemDto } from '../models';
import { RestfulResultPagedListIndexPositionListItem } from '../models';
import { RestfulResultPagedListIndexPositionListItemOriginal } from '../models';
import { RestfulResultPositionDisplayDto } from '../models';
import { RestfulResultPositionDisplayForCompus } from '../models';
import { RestfulResultSearchOptions } from '../models';
import { RestfulResultSlidePostionInfoOutput } from '../models';
import { RestfulResultSubscribePositionInfoOutput } from '../models';
import { SubscribePositionInfoInput } from '../models';
/**
 * PositionApi - axios parameter creator
 * @export
 */
export const PositionApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary logo文章推送
         * @param {ArticlePushRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionArticlePositionSearchPost: async (body?: ArticlePushRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/ArticlePositionSearch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取职位类型对应的职位
         * @param {number} [pageIndex] 当前页码
         * @param {number} [district] 工作地点
         * @param {BussDistrict} [bussDistrictId] 站点
         * @param {string} [postionCareeStr] 职位类型id字符串通过逗号拼接
         * @param {number} [highlight] 是否高亮
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionBluecollarPostionListGet: async (pageIndex?: number, district?: number, bussDistrictId?: BussDistrict, postionCareeStr?: string, highlight?: number, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/BluecollarPostionList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (pageIndex !== undefined) {
                localVarQueryParameter['PageIndex'] = pageIndex;
            }

            if (district !== undefined) {
                localVarQueryParameter['District'] = district;
            }

            if (bussDistrictId !== undefined) {
                localVarQueryParameter['BussDistrictId'] = bussDistrictId;
            }

            if (postionCareeStr !== undefined) {
                localVarQueryParameter['PostionCareeStr'] = postionCareeStr;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['Highlight'] = highlight;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 每日优选职位：设置不感兴趣
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionCancelNotInterestedDailyRecommendGuidPost: async (guid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiPositionCancelNotInterestedDailyRecommendGuidPost.');
            }
            const localVarPath = `/api/Position/CancelNotInterestedDailyRecommend/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 取消订阅
         * @param {number} [infoId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionCancelSubscribePositionInfoDelete: async (infoId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/CancelSubscribePositionInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (infoId !== undefined) {
                localVarQueryParameter['infoId'] = infoId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 清除职位搜索滑动记录
         * @param {string} [searchId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionClearSlideLoadSearchPost: async (searchId?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/ClearSlideLoadSearch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (searchId !== undefined) {
                localVarQueryParameter['searchId'] = searchId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 统计用户浏览职位，公司简介等信息(旧版appCount/DetailsCount接口)
         * @param {PositionClickFrom} [from] 职位点击来源
         * @param {number} [pid] 职位id
         * @param {number} [eid] 企业id
         * @param {number} [isPosiCliLog] 是否需要插入职位点击日志 0:不需要 1:需要  因为在职位详情页面那里也会插入一条点击日志到ClickPosition造成了一次插入两条职位点击日志，所以让前端决定需不需要写日志  如果是点击后跳转职位详情页面就不用写日志了
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionClickGet: async (from?: PositionClickFrom, pid?: number, eid?: number, isPosiCliLog?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/Click`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (pid !== undefined) {
                localVarQueryParameter['pid'] = pid;
            }

            if (eid !== undefined) {
                localVarQueryParameter['eid'] = eid;
            }

            if (isPosiCliLog !== undefined) {
                localVarQueryParameter['isPosiCliLog'] = isPosiCliLog;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 关闭订阅提示，7天不显示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionCloseSubscribePositionDelete: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/CloseSubscribePosition`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 创建订阅
         * @param {SubscribePositionInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionCreateSubscribePositionInfoPost: async (body?: SubscribePositionInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/CreateSubscribePositionInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {string} [trackingGuid] 
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionDetailForCompusGet: async (id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/DetailForCompus`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            if (trackingGuid !== undefined) {
                localVarQueryParameter['trackingGuid'] = trackingGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {string} [trackingGuid] 
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {number} [isRecommend] 是否推荐来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionDetailGet: async (id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, isRecommend?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/Detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (id !== undefined) {
                localVarQueryParameter['id'] = id;
            }

            if (trackingGuid !== undefined) {
                localVarQueryParameter['trackingGuid'] = trackingGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (isRecommend !== undefined) {
                localVarQueryParameter['isRecommend'] = isRecommend;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取校园大屏职位id
         * @param {number} [campusId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionGetCampusScreenPosiIdPost: async (campusId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/GetCampusScreenPosiId`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (campusId !== undefined) {
                localVarQueryParameter['campusId'] = campusId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 每日优选职位：获取每日优选职位
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionGetDailyRecommendGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/GetDailyRecommend`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取兼职职位数据
         * @param {BussDistrict} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionGetPartTimePoitionDatasGet: async (district?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/GetPartTimePoitionDatas`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (district !== undefined) {
                localVarQueryParameter['district'] = district;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取实习职位数据
         * @param {BussDistrict} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionGetTraineePoitionDatasGet: async (district?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/GetTraineePoitionDatas`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (district !== undefined) {
                localVarQueryParameter['district'] = district;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 每日优选职位：判断通知页面的今日优选职位是否提示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionHasViewDailyRecommendGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/HasViewDailyRecommend`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 判断是否显示订阅提示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionJuageShowSubscribeGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/JuageShowSubscribe`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 每日优选职位：设置不感兴趣
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionNotInterestedDailyRecommendGuidPost: async (guid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiPositionNotInterestedDailyRecommendGuidPost.');
            }
            const localVarPath = `/api/Position/NotInterestedDailyRecommend/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 蓝领的职位分类
         * @param {string} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionPostionCareeListGet: async (district?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/PostionCareeList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (district !== undefined) {
                localVarQueryParameter['district'] = district;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位推荐/职位匹配(大数据接口) 带筛选条件
         * @param {PositionRecommendRequest} [body] q
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionRecommendByCareePost: async (body?: PositionRecommendRequest, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/RecommendByCaree`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位推荐/职位匹配(App)
         * @param {BussDistrict} [districtId] 业务地市id
         * @param {number} [positionCareeID] 职位推荐&#x3D;0，其他传职能id
         * @param {number} [sort] 只对id&#x3D;0起作用。最新&#x3D;1（默认）、附近&#x3D;2
         * @param {string} [location] lat,lon
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {number} [isAddPush] 是否添加推送信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionRecommendListGet: async (districtId?: BussDistrict, positionCareeID?: number, sort?: number, location?: string, page?: number, pageSize?: number, isAddPush?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/RecommendList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (positionCareeID !== undefined) {
                localVarQueryParameter['positionCareeID'] = positionCareeID;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }

            if (location !== undefined) {
                localVarQueryParameter['location'] = location;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (isAddPush !== undefined) {
                localVarQueryParameter['isAddPush'] = isAddPush;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索配置
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSearchOptionGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/SearchOption`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位搜索直接返回原始数据(PC)
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSearchOriginalPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/SearchOriginal`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSearchPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/Search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职位搜索加上推送广告
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSearchWithPushPost: async (body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/SearchWithPush`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 每日优选职位：设置投递(只更新推荐职位数据的投递状态，不执行实际的投递操作)
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSetDailyRecommendDeliveredGuidPost: async (guid: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'guid' is not null or undefined
            if (guid === null || guid === undefined) {
                throw new RequiredError('guid','Required parameter guid was null or undefined when calling apiPositionSetDailyRecommendDeliveredGuidPost.');
            }
            const localVarPath = `/api/Position/SetDailyRecommendDelivered/{guid}`
                .replace(`{${"guid"}}`, encodeURIComponent(String(guid)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 相似职位
         * @param {string} [positionGuid] 职位guid
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSimilarsGet: async (positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/Similars`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (positionGuid !== undefined) {
                localVarQueryParameter['positionGuid'] = positionGuid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 右滑动加载搜索的上一条、下一条职位guid
         * @param {string} [searchId] 搜索的id
         * @param {string} [currentPositionGuid] 当前的职位guid
         * @param {number} [page] 所属页码
         * @param {EDataType} [dataType] 数据类型 0：搜索，1：推荐
         * @param {ApplicationPlatform} [from] 应用平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSlideLoadPositionGuidGet: async (searchId?: string, currentPositionGuid?: string, page?: number, dataType?: EDataType, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/SlideLoadPositionGuid`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (searchId !== undefined) {
                localVarQueryParameter['SearchId'] = searchId;
            }

            if (currentPositionGuid !== undefined) {
                localVarQueryParameter['CurrentPositionGuid'] = currentPositionGuid;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (dataType !== undefined) {
                localVarQueryParameter['DataType'] = dataType;
            }

            if (from !== undefined) {
                localVarQueryParameter['From'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取订阅详细
         * @param {number} [infoId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSubscribePositionInfoGet: async (infoId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/SubscribePositionInfo`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (infoId !== undefined) {
                localVarQueryParameter['infoId'] = infoId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取订阅数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionSubscribePositionInfoListGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/SubscribePositionInfoList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置减少推荐职位
         * @param {RecommendUninterested} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionUninterestedPost: async (body?: RecommendUninterested, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Position/Uninterested`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新订阅
         * @param {number} id 
         * @param {SubscribePositionInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiPositionUpdateSubscribePositionInfoIdPost: async (id: number, body?: SubscribePositionInfoInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            if (id === null || id === undefined) {
                throw new RequiredError('id','Required parameter id was null or undefined when calling apiPositionUpdateSubscribePositionInfoIdPost.');
            }
            const localVarPath = `/api/Position/UpdateSubscribePositionInfo/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * PositionApi - functional programming interface
 * @export
 */
export const PositionApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary logo文章推送
         * @param {ArticlePushRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionArticlePositionSearchPost(body?: ArticlePushRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexArticlePushItemDto>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionArticlePositionSearchPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取职位类型对应的职位
         * @param {number} [pageIndex] 当前页码
         * @param {number} [district] 工作地点
         * @param {BussDistrict} [bussDistrictId] 站点
         * @param {string} [postionCareeStr] 职位类型id字符串通过逗号拼接
         * @param {number} [highlight] 是否高亮
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionBluecollarPostionListGet(pageIndex?: number, district?: number, bussDistrictId?: BussDistrict, postionCareeStr?: string, highlight?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionBluecollarPostionListGet(pageIndex, district, bussDistrictId, postionCareeStr, highlight, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 每日优选职位：设置不感兴趣
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCancelNotInterestedDailyRecommendGuidPost(guid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionCancelNotInterestedDailyRecommendGuidPost(guid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 取消订阅
         * @param {number} [infoId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCancelSubscribePositionInfoDelete(infoId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionCancelSubscribePositionInfoDelete(infoId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 清除职位搜索滑动记录
         * @param {string} [searchId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionClearSlideLoadSearchPost(searchId?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionClearSlideLoadSearchPost(searchId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 统计用户浏览职位，公司简介等信息(旧版appCount/DetailsCount接口)
         * @param {PositionClickFrom} [from] 职位点击来源
         * @param {number} [pid] 职位id
         * @param {number} [eid] 企业id
         * @param {number} [isPosiCliLog] 是否需要插入职位点击日志 0:不需要 1:需要  因为在职位详情页面那里也会插入一条点击日志到ClickPosition造成了一次插入两条职位点击日志，所以让前端决定需不需要写日志  如果是点击后跳转职位详情页面就不用写日志了
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionClickGet(from?: PositionClickFrom, pid?: number, eid?: number, isPosiCliLog?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionClickGet(from, pid, eid, isPosiCliLog, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 关闭订阅提示，7天不显示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCloseSubscribePositionDelete(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionCloseSubscribePositionDelete(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 创建订阅
         * @param {SubscribePositionInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCreateSubscribePositionInfoPost(body?: SubscribePositionInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionCreateSubscribePositionInfoPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {string} [trackingGuid] 
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionDetailForCompusGet(id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPositionDisplayForCompus>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionDetailForCompusGet(id, trackingGuid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {string} [trackingGuid] 
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {number} [isRecommend] 是否推荐来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionDetailGet(id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, isRecommend?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPositionDisplayDto>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionDetailGet(id, trackingGuid, districtId, from, isRecommend, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取校园大屏职位id
         * @param {number} [campusId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetCampusScreenPosiIdPost(campusId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListCampusScreenPositionItemDto>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionGetCampusScreenPosiIdPost(campusId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 每日优选职位：获取每日优选职位
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetDailyRecommendGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultIEnumerableDailyRecommendOutput>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionGetDailyRecommendGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取兼职职位数据
         * @param {BussDistrict} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetPartTimePoitionDatasGet(district?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListPositionInfoForDefaultOTO>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionGetPartTimePoitionDatasGet(district, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取实习职位数据
         * @param {BussDistrict} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetTraineePoitionDatasGet(district?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListPositionInfoForDefaultOTO>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionGetTraineePoitionDatasGet(district, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 每日优选职位：判断通知页面的今日优选职位是否提示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionHasViewDailyRecommendGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionHasViewDailyRecommendGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 判断是否显示订阅提示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionJuageShowSubscribeGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultJuageShowSubscribeOutput>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionJuageShowSubscribeGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 每日优选职位：设置不感兴趣
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionNotInterestedDailyRecommendGuidPost(guid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionNotInterestedDailyRecommendGuidPost(guid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 蓝领的职位分类
         * @param {string} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionPostionCareeListGet(district?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListBlueFloorFirst>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionPostionCareeListGet(district, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位推荐/职位匹配(大数据接口) 带筛选条件
         * @param {PositionRecommendRequest} [body] q
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionRecommendByCareePost(body?: PositionRecommendRequest, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListAppPositionRecommendItemModel>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionRecommendByCareePost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位推荐/职位匹配(App)
         * @param {BussDistrict} [districtId] 业务地市id
         * @param {number} [positionCareeID] 职位推荐&#x3D;0，其他传职能id
         * @param {number} [sort] 只对id&#x3D;0起作用。最新&#x3D;1（默认）、附近&#x3D;2
         * @param {string} [location] lat,lon
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {number} [isAddPush] 是否添加推送信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionRecommendListGet(districtId?: BussDistrict, positionCareeID?: number, sort?: number, location?: string, page?: number, pageSize?: number, isAddPush?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListAppPositionRecommendItemModel>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionRecommendListGet(districtId, positionCareeID, sort, location, page, pageSize, isAddPush, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 搜索配置
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchOptionGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSearchOptions>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSearchOptionGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位搜索直接返回原始数据(PC)
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchOriginalPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItemOriginal>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSearchOriginalPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSearchPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职位搜索加上推送广告
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchWithPushPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSearchWithPushPost(body, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 每日优选职位：设置投递(只更新推荐职位数据的投递状态，不执行实际的投递操作)
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSetDailyRecommendDeliveredGuidPost(guid: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSetDailyRecommendDeliveredGuidPost(guid, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 相似职位
         * @param {string} [positionGuid] 职位guid
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSimilarsGet(positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListPositionListItem>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSimilarsGet(positionGuid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 右滑动加载搜索的上一条、下一条职位guid
         * @param {string} [searchId] 搜索的id
         * @param {string} [currentPositionGuid] 当前的职位guid
         * @param {number} [page] 所属页码
         * @param {EDataType} [dataType] 数据类型 0：搜索，1：推荐
         * @param {ApplicationPlatform} [from] 应用平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSlideLoadPositionGuidGet(searchId?: string, currentPositionGuid?: string, page?: number, dataType?: EDataType, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSlidePostionInfoOutput>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSlideLoadPositionGuidGet(searchId, currentPositionGuid, page, dataType, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取订阅详细
         * @param {number} [infoId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSubscribePositionInfoGet(infoId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSubscribePositionInfoOutput>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSubscribePositionInfoGet(infoId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取订阅数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSubscribePositionInfoListGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListSubscribePositionInfoItemOutput>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionSubscribePositionInfoListGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 设置减少推荐职位
         * @param {RecommendUninterested} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionUninterestedPost(body?: RecommendUninterested, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<ApiResultModel>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionUninterestedPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 更新订阅
         * @param {number} id 
         * @param {SubscribePositionInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionUpdateSubscribePositionInfoIdPost(id: number, body?: SubscribePositionInfoInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>>> {
            const localVarAxiosArgs = await PositionApiAxiosParamCreator(configuration).apiPositionUpdateSubscribePositionInfoIdPost(id, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * PositionApi - factory interface
 * @export
 */
export const PositionApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary logo文章推送
         * @param {ArticlePushRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionArticlePositionSearchPost(body?: ArticlePushRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexArticlePushItemDto>> {
            return PositionApiFp(configuration).apiPositionArticlePositionSearchPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取职位类型对应的职位
         * @param {number} [pageIndex] 当前页码
         * @param {number} [district] 工作地点
         * @param {BussDistrict} [bussDistrictId] 站点
         * @param {string} [postionCareeStr] 职位类型id字符串通过逗号拼接
         * @param {number} [highlight] 是否高亮
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionBluecollarPostionListGet(pageIndex?: number, district?: number, bussDistrictId?: BussDistrict, postionCareeStr?: string, highlight?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
            return PositionApiFp(configuration).apiPositionBluecollarPostionListGet(pageIndex, district, bussDistrictId, postionCareeStr, highlight, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 每日优选职位：设置不感兴趣
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCancelNotInterestedDailyRecommendGuidPost(guid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionCancelNotInterestedDailyRecommendGuidPost(guid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 取消订阅
         * @param {number} [infoId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCancelSubscribePositionInfoDelete(infoId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionCancelSubscribePositionInfoDelete(infoId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 清除职位搜索滑动记录
         * @param {string} [searchId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionClearSlideLoadSearchPost(searchId?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionClearSlideLoadSearchPost(searchId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 统计用户浏览职位，公司简介等信息(旧版appCount/DetailsCount接口)
         * @param {PositionClickFrom} [from] 职位点击来源
         * @param {number} [pid] 职位id
         * @param {number} [eid] 企业id
         * @param {number} [isPosiCliLog] 是否需要插入职位点击日志 0:不需要 1:需要  因为在职位详情页面那里也会插入一条点击日志到ClickPosition造成了一次插入两条职位点击日志，所以让前端决定需不需要写日志  如果是点击后跳转职位详情页面就不用写日志了
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionClickGet(from?: PositionClickFrom, pid?: number, eid?: number, isPosiCliLog?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return PositionApiFp(configuration).apiPositionClickGet(from, pid, eid, isPosiCliLog, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 关闭订阅提示，7天不显示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCloseSubscribePositionDelete(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionCloseSubscribePositionDelete(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 创建订阅
         * @param {SubscribePositionInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionCreateSubscribePositionInfoPost(body?: SubscribePositionInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionCreateSubscribePositionInfoPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {string} [trackingGuid] 
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionDetailForCompusGet(id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPositionDisplayForCompus>> {
            return PositionApiFp(configuration).apiPositionDetailForCompusGet(id, trackingGuid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位详情
         * @param {string} [id] 职位ID
         * @param {string} [trackingGuid] 
         * @param {BussDistrict} [districtId] 地市来源
         * @param {ApplicationPlatform} [from] 地市来源
         * @param {number} [isRecommend] 是否推荐来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionDetailGet(id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, isRecommend?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPositionDisplayDto>> {
            return PositionApiFp(configuration).apiPositionDetailGet(id, trackingGuid, districtId, from, isRecommend, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取校园大屏职位id
         * @param {number} [campusId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetCampusScreenPosiIdPost(campusId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListCampusScreenPositionItemDto>> {
            return PositionApiFp(configuration).apiPositionGetCampusScreenPosiIdPost(campusId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 每日优选职位：获取每日优选职位
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetDailyRecommendGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultIEnumerableDailyRecommendOutput>> {
            return PositionApiFp(configuration).apiPositionGetDailyRecommendGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取兼职职位数据
         * @param {BussDistrict} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetPartTimePoitionDatasGet(district?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListPositionInfoForDefaultOTO>> {
            return PositionApiFp(configuration).apiPositionGetPartTimePoitionDatasGet(district, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取实习职位数据
         * @param {BussDistrict} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionGetTraineePoitionDatasGet(district?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListPositionInfoForDefaultOTO>> {
            return PositionApiFp(configuration).apiPositionGetTraineePoitionDatasGet(district, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 每日优选职位：判断通知页面的今日优选职位是否提示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionHasViewDailyRecommendGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionHasViewDailyRecommendGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 判断是否显示订阅提示
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionJuageShowSubscribeGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultJuageShowSubscribeOutput>> {
            return PositionApiFp(configuration).apiPositionJuageShowSubscribeGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 每日优选职位：设置不感兴趣
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionNotInterestedDailyRecommendGuidPost(guid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionNotInterestedDailyRecommendGuidPost(guid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 蓝领的职位分类
         * @param {string} [district] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionPostionCareeListGet(district?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListBlueFloorFirst>> {
            return PositionApiFp(configuration).apiPositionPostionCareeListGet(district, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位推荐/职位匹配(大数据接口) 带筛选条件
         * @param {PositionRecommendRequest} [body] q
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionRecommendByCareePost(body?: PositionRecommendRequest, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListAppPositionRecommendItemModel>> {
            return PositionApiFp(configuration).apiPositionRecommendByCareePost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位推荐/职位匹配(App)
         * @param {BussDistrict} [districtId] 业务地市id
         * @param {number} [positionCareeID] 职位推荐&#x3D;0，其他传职能id
         * @param {number} [sort] 只对id&#x3D;0起作用。最新&#x3D;1（默认）、附近&#x3D;2
         * @param {string} [location] lat,lon
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {number} [isAddPush] 是否添加推送信息
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionRecommendListGet(districtId?: BussDistrict, positionCareeID?: number, sort?: number, location?: string, page?: number, pageSize?: number, isAddPush?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListAppPositionRecommendItemModel>> {
            return PositionApiFp(configuration).apiPositionRecommendListGet(districtId, positionCareeID, sort, location, page, pageSize, isAddPush, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索配置
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchOptionGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSearchOptions>> {
            return PositionApiFp(configuration).apiPositionSearchOptionGet(districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位搜索直接返回原始数据(PC)
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchOriginalPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItemOriginal>> {
            return PositionApiFp(configuration).apiPositionSearchOriginalPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位搜索
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
            return PositionApiFp(configuration).apiPositionSearchPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职位搜索加上推送广告
         * @param {PositionRequest} [body] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSearchWithPushPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
            return PositionApiFp(configuration).apiPositionSearchWithPushPost(body, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 每日优选职位：设置投递(只更新推荐职位数据的投递状态，不执行实际的投递操作)
         * @param {string} guid 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSetDailyRecommendDeliveredGuidPost(guid: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionSetDailyRecommendDeliveredGuidPost(guid, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 相似职位
         * @param {string} [positionGuid] 职位guid
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSimilarsGet(positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListPositionListItem>> {
            return PositionApiFp(configuration).apiPositionSimilarsGet(positionGuid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 右滑动加载搜索的上一条、下一条职位guid
         * @param {string} [searchId] 搜索的id
         * @param {string} [currentPositionGuid] 当前的职位guid
         * @param {number} [page] 所属页码
         * @param {EDataType} [dataType] 数据类型 0：搜索，1：推荐
         * @param {ApplicationPlatform} [from] 应用平台
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSlideLoadPositionGuidGet(searchId?: string, currentPositionGuid?: string, page?: number, dataType?: EDataType, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSlidePostionInfoOutput>> {
            return PositionApiFp(configuration).apiPositionSlideLoadPositionGuidGet(searchId, currentPositionGuid, page, dataType, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取订阅详细
         * @param {number} [infoId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSubscribePositionInfoGet(infoId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSubscribePositionInfoOutput>> {
            return PositionApiFp(configuration).apiPositionSubscribePositionInfoGet(infoId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取订阅数据
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionSubscribePositionInfoListGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListSubscribePositionInfoItemOutput>> {
            return PositionApiFp(configuration).apiPositionSubscribePositionInfoListGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置减少推荐职位
         * @param {RecommendUninterested} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionUninterestedPost(body?: RecommendUninterested, options?: AxiosRequestConfig): Promise<AxiosResponse<ApiResultModel>> {
            return PositionApiFp(configuration).apiPositionUninterestedPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新订阅
         * @param {number} id 
         * @param {SubscribePositionInfoInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiPositionUpdateSubscribePositionInfoIdPost(id: number, body?: SubscribePositionInfoInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
            return PositionApiFp(configuration).apiPositionUpdateSubscribePositionInfoIdPost(id, body, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * PositionApi - object-oriented interface
 * @export
 * @class PositionApi
 * @extends {BaseAPI}
 */
export class PositionApi extends BaseAPI {
    /**
     * 
     * @summary logo文章推送
     * @param {ArticlePushRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionArticlePositionSearchPost(body?: ArticlePushRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexArticlePushItemDto>> {
        return PositionApiFp(this.configuration).apiPositionArticlePositionSearchPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取职位类型对应的职位
     * @param {number} [pageIndex] 当前页码
     * @param {number} [district] 工作地点
     * @param {BussDistrict} [bussDistrictId] 站点
     * @param {string} [postionCareeStr] 职位类型id字符串通过逗号拼接
     * @param {number} [highlight] 是否高亮
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionBluecollarPostionListGet(pageIndex?: number, district?: number, bussDistrictId?: BussDistrict, postionCareeStr?: string, highlight?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
        return PositionApiFp(this.configuration).apiPositionBluecollarPostionListGet(pageIndex, district, bussDistrictId, postionCareeStr, highlight, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 每日优选职位：设置不感兴趣
     * @param {string} guid 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionCancelNotInterestedDailyRecommendGuidPost(guid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionCancelNotInterestedDailyRecommendGuidPost(guid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 取消订阅
     * @param {number} [infoId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionCancelSubscribePositionInfoDelete(infoId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionCancelSubscribePositionInfoDelete(infoId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 清除职位搜索滑动记录
     * @param {string} [searchId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionClearSlideLoadSearchPost(searchId?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionClearSlideLoadSearchPost(searchId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 统计用户浏览职位，公司简介等信息(旧版appCount/DetailsCount接口)
     * @param {PositionClickFrom} [from] 职位点击来源
     * @param {number} [pid] 职位id
     * @param {number} [eid] 企业id
     * @param {number} [isPosiCliLog] 是否需要插入职位点击日志 0:不需要 1:需要  因为在职位详情页面那里也会插入一条点击日志到ClickPosition造成了一次插入两条职位点击日志，所以让前端决定需不需要写日志  如果是点击后跳转职位详情页面就不用写日志了
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionClickGet(from?: PositionClickFrom, pid?: number, eid?: number, isPosiCliLog?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return PositionApiFp(this.configuration).apiPositionClickGet(from, pid, eid, isPosiCliLog, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 关闭订阅提示，7天不显示
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionCloseSubscribePositionDelete(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionCloseSubscribePositionDelete(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 创建订阅
     * @param {SubscribePositionInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionCreateSubscribePositionInfoPost(body?: SubscribePositionInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionCreateSubscribePositionInfoPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位详情
     * @param {string} [id] 职位ID
     * @param {string} [trackingGuid] 
     * @param {BussDistrict} [districtId] 地市来源
     * @param {ApplicationPlatform} [from] 地市来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionDetailForCompusGet(id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPositionDisplayForCompus>> {
        return PositionApiFp(this.configuration).apiPositionDetailForCompusGet(id, trackingGuid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位详情
     * @param {string} [id] 职位ID
     * @param {string} [trackingGuid] 
     * @param {BussDistrict} [districtId] 地市来源
     * @param {ApplicationPlatform} [from] 地市来源
     * @param {number} [isRecommend] 是否推荐来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionDetailGet(id?: string, trackingGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, isRecommend?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPositionDisplayDto>> {
        return PositionApiFp(this.configuration).apiPositionDetailGet(id, trackingGuid, districtId, from, isRecommend, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取校园大屏职位id
     * @param {number} [campusId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionGetCampusScreenPosiIdPost(campusId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListCampusScreenPositionItemDto>> {
        return PositionApiFp(this.configuration).apiPositionGetCampusScreenPosiIdPost(campusId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 每日优选职位：获取每日优选职位
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionGetDailyRecommendGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultIEnumerableDailyRecommendOutput>> {
        return PositionApiFp(this.configuration).apiPositionGetDailyRecommendGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取兼职职位数据
     * @param {BussDistrict} [district] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionGetPartTimePoitionDatasGet(district?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListPositionInfoForDefaultOTO>> {
        return PositionApiFp(this.configuration).apiPositionGetPartTimePoitionDatasGet(district, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取实习职位数据
     * @param {BussDistrict} [district] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionGetTraineePoitionDatasGet(district?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListPositionInfoForDefaultOTO>> {
        return PositionApiFp(this.configuration).apiPositionGetTraineePoitionDatasGet(district, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 每日优选职位：判断通知页面的今日优选职位是否提示
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionHasViewDailyRecommendGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionHasViewDailyRecommendGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 判断是否显示订阅提示
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionJuageShowSubscribeGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultJuageShowSubscribeOutput>> {
        return PositionApiFp(this.configuration).apiPositionJuageShowSubscribeGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 每日优选职位：设置不感兴趣
     * @param {string} guid 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionNotInterestedDailyRecommendGuidPost(guid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionNotInterestedDailyRecommendGuidPost(guid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 蓝领的职位分类
     * @param {string} [district] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionPostionCareeListGet(district?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListBlueFloorFirst>> {
        return PositionApiFp(this.configuration).apiPositionPostionCareeListGet(district, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位推荐/职位匹配(大数据接口) 带筛选条件
     * @param {PositionRecommendRequest} [body] q
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionRecommendByCareePost(body?: PositionRecommendRequest, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListAppPositionRecommendItemModel>> {
        return PositionApiFp(this.configuration).apiPositionRecommendByCareePost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位推荐/职位匹配(App)
     * @param {BussDistrict} [districtId] 业务地市id
     * @param {number} [positionCareeID] 职位推荐&#x3D;0，其他传职能id
     * @param {number} [sort] 只对id&#x3D;0起作用。最新&#x3D;1（默认）、附近&#x3D;2
     * @param {string} [location] lat,lon
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {number} [isAddPush] 是否添加推送信息
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionRecommendListGet(districtId?: BussDistrict, positionCareeID?: number, sort?: number, location?: string, page?: number, pageSize?: number, isAddPush?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListAppPositionRecommendItemModel>> {
        return PositionApiFp(this.configuration).apiPositionRecommendListGet(districtId, positionCareeID, sort, location, page, pageSize, isAddPush, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 搜索配置
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSearchOptionGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSearchOptions>> {
        return PositionApiFp(this.configuration).apiPositionSearchOptionGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位搜索直接返回原始数据(PC)
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSearchOriginalPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItemOriginal>> {
        return PositionApiFp(this.configuration).apiPositionSearchOriginalPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位搜索
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSearchPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
        return PositionApiFp(this.configuration).apiPositionSearchPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职位搜索加上推送广告
     * @param {PositionRequest} [body] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSearchWithPushPost(body?: PositionRequest, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
        return PositionApiFp(this.configuration).apiPositionSearchWithPushPost(body, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 每日优选职位：设置投递(只更新推荐职位数据的投递状态，不执行实际的投递操作)
     * @param {string} guid 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSetDailyRecommendDeliveredGuidPost(guid: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionSetDailyRecommendDeliveredGuidPost(guid, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 相似职位
     * @param {string} [positionGuid] 职位guid
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSimilarsGet(positionGuid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListPositionListItem>> {
        return PositionApiFp(this.configuration).apiPositionSimilarsGet(positionGuid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 右滑动加载搜索的上一条、下一条职位guid
     * @param {string} [searchId] 搜索的id
     * @param {string} [currentPositionGuid] 当前的职位guid
     * @param {number} [page] 所属页码
     * @param {EDataType} [dataType] 数据类型 0：搜索，1：推荐
     * @param {ApplicationPlatform} [from] 应用平台
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSlideLoadPositionGuidGet(searchId?: string, currentPositionGuid?: string, page?: number, dataType?: EDataType, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSlidePostionInfoOutput>> {
        return PositionApiFp(this.configuration).apiPositionSlideLoadPositionGuidGet(searchId, currentPositionGuid, page, dataType, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取订阅详细
     * @param {number} [infoId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSubscribePositionInfoGet(infoId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSubscribePositionInfoOutput>> {
        return PositionApiFp(this.configuration).apiPositionSubscribePositionInfoGet(infoId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取订阅数据
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionSubscribePositionInfoListGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListSubscribePositionInfoItemOutput>> {
        return PositionApiFp(this.configuration).apiPositionSubscribePositionInfoListGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 设置减少推荐职位
     * @param {RecommendUninterested} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionUninterestedPost(body?: RecommendUninterested, options?: AxiosRequestConfig) : Promise<AxiosResponse<ApiResultModel>> {
        return PositionApiFp(this.configuration).apiPositionUninterestedPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 更新订阅
     * @param {number} id 
     * @param {SubscribePositionInfoInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof PositionApi
     */
    public async apiPositionUpdateSubscribePositionInfoIdPost(id: number, body?: SubscribePositionInfoInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultBaseDataOutputBoolean>> {
        return PositionApiFp(this.configuration).apiPositionUpdateSubscribePositionInfoIdPost(id, body, options).then((request) => request(this.axios, this.basePath));
    }
}
