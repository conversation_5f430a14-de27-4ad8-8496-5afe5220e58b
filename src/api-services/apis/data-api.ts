/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { RestfulResultAgreementCtrlDto } from '../models';
import { RestfulResultAppPromotionsDto } from '../models';
import { RestfulResultAppRecommendOptionListModel } from '../models';
import { RestfulResultAppVersionDto } from '../models';
import { RestfulResultIndexLogoCellDto } from '../models';
import { RestfulResultListBusinessDistrictOutput } from '../models';
import { RestfulResultListCityBusinessDto } from '../models';
import { RestfulResultListDictionarySqlEntity } from '../models';
import { RestfulResultListKeywordItemDto } from '../models';
import { RestfulResultListTopicOnNewEnergyItemDto } from '../models';
import { RestfulResultPagedListIndexPositionListItem } from '../models';
import { RestfulResultPagedListTopicSearchEnterpriseOutput } from '../models';
import { RestfulResultReportInformationPromptModel } from '../models';
import { RestfulResultSharePositionUrlDto } from '../models';
import { RestfulResultSubwayZoneDto } from '../models';
import { RestfulResultSysCtrlViewModel } from '../models';
import { RestfulResultWlxtSettingDto } from '../models';
import { TopicPositionClickInput } from '../models';
import { TopicSearchEnterpriseInput } from '../models';
import { TopicSearchInput } from '../models';
/**
 * DataApi - axios parameter creator
 * @export
 */
export const DataApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 系统控制个人隐私协议显示(旧版appHome/AgreementCtrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataAgreementCtrlGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/AgreementCtrl`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询所有商业区域信息列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataAllBusinessDistrictsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/allBusinessDistricts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 系统控制接口(旧版appHome/AppSysCtrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataAppSysCtrlGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/AppSysCtrl`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据城市字典ID查询该城市内的所有商业区域信息列表
         * @param {number} cityDictId 城市字典ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataBusinessDistrictsByCityCityDictIdGet: async (cityDictId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'cityDictId' is not null or undefined
            if (cityDictId === null || cityDictId === undefined) {
                throw new RequiredError('cityDictId','Required parameter cityDictId was null or undefined when calling apiDataBusinessDistrictsByCityCityDictIdGet.');
            }
            const localVarPath = `/api/Data/businessDistrictsByCity/{cityDictId}`
                .replace(`{${"cityDictId"}}`, encodeURIComponent(String(cityDictId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据地区字典ID查询该地区内的所有商业区域信息列表
         * @param {number} districtDictId 地区字典ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataBusinessDistrictsByDistrictDistrictDictIdGet: async (districtDictId: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'districtDictId' is not null or undefined
            if (districtDictId === null || districtDictId === undefined) {
                throw new RequiredError('districtDictId','Required parameter districtDictId was null or undefined when calling apiDataBusinessDistrictsByDistrictDistrictDictIdGet.');
            }
            const localVarPath = `/api/Data/businessDistrictsByDistrict/{districtDictId}`
                .replace(`{${"districtDictId"}}`, encodeURIComponent(String(districtDictId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取举报信息提示(旧版video/complainmessage接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataComplainMessageGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/ComplainMessage`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 城市数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataDistrictGet: async (parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/District`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (parentID !== undefined) {
                localVarQueryParameter['parentID'] = parentID;
            }

            if (withCache !== undefined) {
                localVarQueryParameter['withCache'] = withCache;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取城市对应的业务ID和字典ID
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetBusinessCityGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetBusinessCity`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据url获取当前请求来源的信息
         * @param {string} [url] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetDistrictIdGet: async (url?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetDistrictId`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (url !== undefined) {
                localVarQueryParameter['url'] = url;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取行业字典信息 只拉取一级大行业
         * @param {boolean} [withCache] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetIndustryOnlyOneDataGet: async (withCache?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetIndustryOnlyOneData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (withCache !== undefined) {
                localVarQueryParameter['withCache'] = withCache;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取推荐不感兴趣/ 效率反馈的字典信息
         * @param {number} [positionId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetRecommendUninterestedDataGet: async (positionId?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetRecommendUninterestedData`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (positionId !== undefined) {
                localVarQueryParameter['positionId'] = positionId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取专题页面相关职位
         * @param {TopicSearchEnterpriseInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetTopicEnterpriseListPost: async (body?: TopicSearchEnterpriseInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetTopicEnterpriseList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取专题页选项
         * @param {number} [topicType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetTopicOnNewEnergyNavGet: async (topicType?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetTopicOnNewEnergyNav`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (topicType !== undefined) {
                localVarQueryParameter['TopicType'] = topicType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取网联配置
         * @param {number} [districtID] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataGetWlxtSettingByDistrictIdGet: async (districtID?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/GetWlxtSettingByDistrictId`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtID !== undefined) {
                localVarQueryParameter['DistrictID'] = districtID;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 行业数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataIndustryGet: async (parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/Industry`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (parentID !== undefined) {
                localVarQueryParameter['parentID'] = parentID;
            }

            if (withCache !== undefined) {
                localVarQueryParameter['withCache'] = withCache;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职能数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataPositionGet: async (parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/Position`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (parentID !== undefined) {
                localVarQueryParameter['parentID'] = parentID;
            }

            if (withCache !== undefined) {
                localVarQueryParameter['withCache'] = withCache;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职能数据 注册使用
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataPositionRegisterGet: async (parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/PositionRegister`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (parentID !== undefined) {
                localVarQueryParameter['parentID'] = parentID;
            }

            if (withCache !== undefined) {
                localVarQueryParameter['withCache'] = withCache;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 促销活动开关和促销活动图标url和ios版本号和活动开始结束时间非时间段内开会自动关闭
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataPromotionsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/Promotions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary App获取首页职位推荐选项(旧版Home/RecommendOptions接口)
         * @param {boolean} [isHideRecommendation] 是否隐藏推荐
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataRecommendOptionsGet: async (isHideRecommendation?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/RecommendOptions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (isHideRecommendation !== undefined) {
                localVarQueryParameter['isHideRecommendation'] = isHideRecommendation;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分享职位的URL前缀(旧版appJob/ShareUrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataShareUrlGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/ShareUrl`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary App获取首页单元格广告(旧版home/smallcells接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataSmallCellsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/SmallCells`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 地铁和距离
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataSubwayZoneGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/SubwayZone`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 专题也职位点击
         * @param {TopicPositionClickInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataTopicPositionClickPost: async (body?: TopicPositionClickInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/TopicPositionClick`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取专题页面相关职位
         * @param {TopicSearchInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataTopicPositionListPost: async (body?: TopicSearchInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/TopicPositionList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary App返回版本号和新版本下载地址(旧版appHome/version接口)
         * @param {BussDistrict} [bussId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDataVersionGet: async (bussId?: BussDistrict, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Data/Version`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (bussId !== undefined) {
                localVarQueryParameter['bussId'] = bussId;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DataApi - functional programming interface
 * @export
 */
export const DataApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 系统控制个人隐私协议显示(旧版appHome/AgreementCtrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataAgreementCtrlGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultAgreementCtrlDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataAgreementCtrlGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 查询所有商业区域信息列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataAllBusinessDistrictsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataAllBusinessDistrictsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 系统控制接口(旧版appHome/AppSysCtrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataAppSysCtrlGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSysCtrlViewModel>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataAppSysCtrlGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据城市字典ID查询该城市内的所有商业区域信息列表
         * @param {number} cityDictId 城市字典ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataBusinessDistrictsByCityCityDictIdGet(cityDictId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataBusinessDistrictsByCityCityDictIdGet(cityDictId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据地区字典ID查询该地区内的所有商业区域信息列表
         * @param {number} districtDictId 地区字典ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataBusinessDistrictsByDistrictDistrictDictIdGet(districtDictId: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataBusinessDistrictsByDistrictDistrictDictIdGet(districtDictId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取举报信息提示(旧版video/complainmessage接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataComplainMessageGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultReportInformationPromptModel>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataComplainMessageGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 城市数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataDistrictGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListKeywordItemDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataDistrictGet(parentID, withCache, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取城市对应的业务ID和字典ID
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetBusinessCityGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListCityBusinessDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetBusinessCityGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 根据url获取当前请求来源的信息
         * @param {string} [url] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetDistrictIdGet(url?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultWlxtSettingDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetDistrictIdGet(url, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取行业字典信息 只拉取一级大行业
         * @param {boolean} [withCache] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetIndustryOnlyOneDataGet(withCache?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListKeywordItemDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetIndustryOnlyOneDataGet(withCache, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取推荐不感兴趣/ 效率反馈的字典信息
         * @param {number} [positionId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetRecommendUninterestedDataGet(positionId?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListDictionarySqlEntity>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetRecommendUninterestedDataGet(positionId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取专题页面相关职位
         * @param {TopicSearchEnterpriseInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetTopicEnterpriseListPost(body?: TopicSearchEnterpriseInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListTopicSearchEnterpriseOutput>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetTopicEnterpriseListPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取专题页选项
         * @param {number} [topicType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetTopicOnNewEnergyNavGet(topicType?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListTopicOnNewEnergyItemDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetTopicOnNewEnergyNavGet(topicType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取网联配置
         * @param {number} [districtID] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetWlxtSettingByDistrictIdGet(districtID?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultWlxtSettingDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataGetWlxtSettingByDistrictIdGet(districtID, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 行业数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataIndustryGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListKeywordItemDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataIndustryGet(parentID, withCache, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职能数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataPositionGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListKeywordItemDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataPositionGet(parentID, withCache, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职能数据 注册使用
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataPositionRegisterGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListKeywordItemDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataPositionRegisterGet(parentID, withCache, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 促销活动开关和促销活动图标url和ios版本号和活动开始结束时间非时间段内开会自动关闭
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataPromotionsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultAppPromotionsDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataPromotionsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary App获取首页职位推荐选项(旧版Home/RecommendOptions接口)
         * @param {boolean} [isHideRecommendation] 是否隐藏推荐
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataRecommendOptionsGet(isHideRecommendation?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultAppRecommendOptionListModel>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataRecommendOptionsGet(isHideRecommendation, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 分享职位的URL前缀(旧版appJob/ShareUrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataShareUrlGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSharePositionUrlDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataShareUrlGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary App获取首页单元格广告(旧版home/smallcells接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataSmallCellsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultIndexLogoCellDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataSmallCellsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 地铁和距离
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataSubwayZoneGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSubwayZoneDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataSubwayZoneGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 专题也职位点击
         * @param {TopicPositionClickInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataTopicPositionClickPost(body?: TopicPositionClickInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<void>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataTopicPositionClickPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取专题页面相关职位
         * @param {TopicSearchInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataTopicPositionListPost(body?: TopicSearchInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataTopicPositionListPost(body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary App返回版本号和新版本下载地址(旧版appHome/version接口)
         * @param {BussDistrict} [bussId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataVersionGet(bussId?: BussDistrict, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultAppVersionDto>>> {
            const localVarAxiosArgs = await DataApiAxiosParamCreator(configuration).apiDataVersionGet(bussId, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DataApi - factory interface
 * @export
 */
export const DataApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 系统控制个人隐私协议显示(旧版appHome/AgreementCtrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataAgreementCtrlGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultAgreementCtrlDto>> {
            return DataApiFp(configuration).apiDataAgreementCtrlGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询所有商业区域信息列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataAllBusinessDistrictsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>> {
            return DataApiFp(configuration).apiDataAllBusinessDistrictsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 系统控制接口(旧版appHome/AppSysCtrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataAppSysCtrlGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSysCtrlViewModel>> {
            return DataApiFp(configuration).apiDataAppSysCtrlGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据城市字典ID查询该城市内的所有商业区域信息列表
         * @param {number} cityDictId 城市字典ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataBusinessDistrictsByCityCityDictIdGet(cityDictId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>> {
            return DataApiFp(configuration).apiDataBusinessDistrictsByCityCityDictIdGet(cityDictId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据地区字典ID查询该地区内的所有商业区域信息列表
         * @param {number} districtDictId 地区字典ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataBusinessDistrictsByDistrictDistrictDictIdGet(districtDictId: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>> {
            return DataApiFp(configuration).apiDataBusinessDistrictsByDistrictDistrictDictIdGet(districtDictId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取举报信息提示(旧版video/complainmessage接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataComplainMessageGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultReportInformationPromptModel>> {
            return DataApiFp(configuration).apiDataComplainMessageGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 城市数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataDistrictGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
            return DataApiFp(configuration).apiDataDistrictGet(parentID, withCache, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取城市对应的业务ID和字典ID
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetBusinessCityGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListCityBusinessDto>> {
            return DataApiFp(configuration).apiDataGetBusinessCityGet(districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据url获取当前请求来源的信息
         * @param {string} [url] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetDistrictIdGet(url?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultWlxtSettingDto>> {
            return DataApiFp(configuration).apiDataGetDistrictIdGet(url, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取行业字典信息 只拉取一级大行业
         * @param {boolean} [withCache] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetIndustryOnlyOneDataGet(withCache?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
            return DataApiFp(configuration).apiDataGetIndustryOnlyOneDataGet(withCache, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取推荐不感兴趣/ 效率反馈的字典信息
         * @param {number} [positionId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetRecommendUninterestedDataGet(positionId?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListDictionarySqlEntity>> {
            return DataApiFp(configuration).apiDataGetRecommendUninterestedDataGet(positionId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取专题页面相关职位
         * @param {TopicSearchEnterpriseInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetTopicEnterpriseListPost(body?: TopicSearchEnterpriseInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListTopicSearchEnterpriseOutput>> {
            return DataApiFp(configuration).apiDataGetTopicEnterpriseListPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取专题页选项
         * @param {number} [topicType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetTopicOnNewEnergyNavGet(topicType?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListTopicOnNewEnergyItemDto>> {
            return DataApiFp(configuration).apiDataGetTopicOnNewEnergyNavGet(topicType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取网联配置
         * @param {number} [districtID] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataGetWlxtSettingByDistrictIdGet(districtID?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultWlxtSettingDto>> {
            return DataApiFp(configuration).apiDataGetWlxtSettingByDistrictIdGet(districtID, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 行业数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataIndustryGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
            return DataApiFp(configuration).apiDataIndustryGet(parentID, withCache, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职能数据
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataPositionGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
            return DataApiFp(configuration).apiDataPositionGet(parentID, withCache, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职能数据 注册使用
         * @param {number} [parentID] 上级ID
         * @param {boolean} [withCache] 是否从缓存里面读取
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataPositionRegisterGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
            return DataApiFp(configuration).apiDataPositionRegisterGet(parentID, withCache, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 促销活动开关和促销活动图标url和ios版本号和活动开始结束时间非时间段内开会自动关闭
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataPromotionsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultAppPromotionsDto>> {
            return DataApiFp(configuration).apiDataPromotionsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary App获取首页职位推荐选项(旧版Home/RecommendOptions接口)
         * @param {boolean} [isHideRecommendation] 是否隐藏推荐
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataRecommendOptionsGet(isHideRecommendation?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultAppRecommendOptionListModel>> {
            return DataApiFp(configuration).apiDataRecommendOptionsGet(isHideRecommendation, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分享职位的URL前缀(旧版appJob/ShareUrl接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataShareUrlGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSharePositionUrlDto>> {
            return DataApiFp(configuration).apiDataShareUrlGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary App获取首页单元格广告(旧版home/smallcells接口)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataSmallCellsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultIndexLogoCellDto>> {
            return DataApiFp(configuration).apiDataSmallCellsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 地铁和距离
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataSubwayZoneGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSubwayZoneDto>> {
            return DataApiFp(configuration).apiDataSubwayZoneGet(districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 专题也职位点击
         * @param {TopicPositionClickInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataTopicPositionClickPost(body?: TopicPositionClickInput, options?: AxiosRequestConfig): Promise<AxiosResponse<void>> {
            return DataApiFp(configuration).apiDataTopicPositionClickPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取专题页面相关职位
         * @param {TopicSearchInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataTopicPositionListPost(body?: TopicSearchInput, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
            return DataApiFp(configuration).apiDataTopicPositionListPost(body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary App返回版本号和新版本下载地址(旧版appHome/version接口)
         * @param {BussDistrict} [bussId] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDataVersionGet(bussId?: BussDistrict, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultAppVersionDto>> {
            return DataApiFp(configuration).apiDataVersionGet(bussId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DataApi - object-oriented interface
 * @export
 * @class DataApi
 * @extends {BaseAPI}
 */
export class DataApi extends BaseAPI {
    /**
     * 
     * @summary 系统控制个人隐私协议显示(旧版appHome/AgreementCtrl接口)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataAgreementCtrlGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultAgreementCtrlDto>> {
        return DataApiFp(this.configuration).apiDataAgreementCtrlGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 查询所有商业区域信息列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataAllBusinessDistrictsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>> {
        return DataApiFp(this.configuration).apiDataAllBusinessDistrictsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 系统控制接口(旧版appHome/AppSysCtrl接口)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataAppSysCtrlGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSysCtrlViewModel>> {
        return DataApiFp(this.configuration).apiDataAppSysCtrlGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据城市字典ID查询该城市内的所有商业区域信息列表
     * @param {number} cityDictId 城市字典ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataBusinessDistrictsByCityCityDictIdGet(cityDictId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>> {
        return DataApiFp(this.configuration).apiDataBusinessDistrictsByCityCityDictIdGet(cityDictId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据地区字典ID查询该地区内的所有商业区域信息列表
     * @param {number} districtDictId 地区字典ID
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataBusinessDistrictsByDistrictDistrictDictIdGet(districtDictId: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListBusinessDistrictOutput>> {
        return DataApiFp(this.configuration).apiDataBusinessDistrictsByDistrictDistrictDictIdGet(districtDictId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取举报信息提示(旧版video/complainmessage接口)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataComplainMessageGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultReportInformationPromptModel>> {
        return DataApiFp(this.configuration).apiDataComplainMessageGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 城市数据
     * @param {number} [parentID] 上级ID
     * @param {boolean} [withCache] 是否从缓存里面读取
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataDistrictGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
        return DataApiFp(this.configuration).apiDataDistrictGet(parentID, withCache, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取城市对应的业务ID和字典ID
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetBusinessCityGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListCityBusinessDto>> {
        return DataApiFp(this.configuration).apiDataGetBusinessCityGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 根据url获取当前请求来源的信息
     * @param {string} [url] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetDistrictIdGet(url?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultWlxtSettingDto>> {
        return DataApiFp(this.configuration).apiDataGetDistrictIdGet(url, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取行业字典信息 只拉取一级大行业
     * @param {boolean} [withCache] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetIndustryOnlyOneDataGet(withCache?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
        return DataApiFp(this.configuration).apiDataGetIndustryOnlyOneDataGet(withCache, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取推荐不感兴趣/ 效率反馈的字典信息
     * @param {number} [positionId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetRecommendUninterestedDataGet(positionId?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListDictionarySqlEntity>> {
        return DataApiFp(this.configuration).apiDataGetRecommendUninterestedDataGet(positionId, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取专题页面相关职位
     * @param {TopicSearchEnterpriseInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetTopicEnterpriseListPost(body?: TopicSearchEnterpriseInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListTopicSearchEnterpriseOutput>> {
        return DataApiFp(this.configuration).apiDataGetTopicEnterpriseListPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取专题页选项
     * @param {number} [topicType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetTopicOnNewEnergyNavGet(topicType?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListTopicOnNewEnergyItemDto>> {
        return DataApiFp(this.configuration).apiDataGetTopicOnNewEnergyNavGet(topicType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取网联配置
     * @param {number} [districtID] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataGetWlxtSettingByDistrictIdGet(districtID?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultWlxtSettingDto>> {
        return DataApiFp(this.configuration).apiDataGetWlxtSettingByDistrictIdGet(districtID, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 行业数据
     * @param {number} [parentID] 上级ID
     * @param {boolean} [withCache] 是否从缓存里面读取
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataIndustryGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
        return DataApiFp(this.configuration).apiDataIndustryGet(parentID, withCache, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职能数据
     * @param {number} [parentID] 上级ID
     * @param {boolean} [withCache] 是否从缓存里面读取
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataPositionGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
        return DataApiFp(this.configuration).apiDataPositionGet(parentID, withCache, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职能数据 注册使用
     * @param {number} [parentID] 上级ID
     * @param {boolean} [withCache] 是否从缓存里面读取
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataPositionRegisterGet(parentID?: number, withCache?: boolean, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListKeywordItemDto>> {
        return DataApiFp(this.configuration).apiDataPositionRegisterGet(parentID, withCache, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 促销活动开关和促销活动图标url和ios版本号和活动开始结束时间非时间段内开会自动关闭
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataPromotionsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultAppPromotionsDto>> {
        return DataApiFp(this.configuration).apiDataPromotionsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary App获取首页职位推荐选项(旧版Home/RecommendOptions接口)
     * @param {boolean} [isHideRecommendation] 是否隐藏推荐
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataRecommendOptionsGet(isHideRecommendation?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultAppRecommendOptionListModel>> {
        return DataApiFp(this.configuration).apiDataRecommendOptionsGet(isHideRecommendation, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 分享职位的URL前缀(旧版appJob/ShareUrl接口)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataShareUrlGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSharePositionUrlDto>> {
        return DataApiFp(this.configuration).apiDataShareUrlGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary App获取首页单元格广告(旧版home/smallcells接口)
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataSmallCellsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultIndexLogoCellDto>> {
        return DataApiFp(this.configuration).apiDataSmallCellsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 地铁和距离
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataSubwayZoneGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSubwayZoneDto>> {
        return DataApiFp(this.configuration).apiDataSubwayZoneGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 专题也职位点击
     * @param {TopicPositionClickInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataTopicPositionClickPost(body?: TopicPositionClickInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<void>> {
        return DataApiFp(this.configuration).apiDataTopicPositionClickPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取专题页面相关职位
     * @param {TopicSearchInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataTopicPositionListPost(body?: TopicSearchInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListIndexPositionListItem>> {
        return DataApiFp(this.configuration).apiDataTopicPositionListPost(body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary App返回版本号和新版本下载地址(旧版appHome/version接口)
     * @param {BussDistrict} [bussId] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DataApi
     */
    public async apiDataVersionGet(bussId?: BussDistrict, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultAppVersionDto>> {
        return DataApiFp(this.configuration).apiDataVersionGet(bussId, options).then((request) => request(this.axios, this.basePath));
    }
}
