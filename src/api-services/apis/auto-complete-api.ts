/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { RestfulResultListDictionaryDocument } from '../models';
import { RestfulResultListPositionInputPromptEntity } from '../models';
/**
 * AutoCompleteApi - axios parameter creator
 * @export
 */
export const AutoCompleteApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 企业名称输入提示
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {string} [color] 高亮颜色 默认#FF4400
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAutoCompleteEnterpriseNameGet: async (keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/AutoComplete/EnterpriseName`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['highlight'] = highlight;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (color !== undefined) {
                localVarQueryParameter['color'] = color;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 行业类型
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAutoCompleteIndustryGet: async (keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/AutoComplete/Industry`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['highlight'] = highlight;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 猜你想找
         * @param {string} [keyword] 关键词
         * @param {number} [type] 职位&#x3D;0、公司&#x3D;1
         * @param {number} [size] 默认返回10个
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAutoCompleteMatchGet: async (keyword?: string, type?: number, size?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/AutoComplete/Match`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (type !== undefined) {
                localVarQueryParameter['type'] = type;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 职能类型
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAutoCompletePositionTypeGet: async (keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/AutoComplete/PositionType`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['highlight'] = highlight;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 校园名称输入提示
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {string} [color] 高亮颜色 默认#FF4400
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiAutoCompleteSchoolNameGet: async (keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/AutoComplete/SchoolName`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (highlight !== undefined) {
                localVarQueryParameter['highlight'] = highlight;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (color !== undefined) {
                localVarQueryParameter['color'] = color;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AutoCompleteApi - functional programming interface
 * @export
 */
export const AutoCompleteApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 企业名称输入提示
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {string} [color] 高亮颜色 默认#FF4400
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteEnterpriseNameGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListDictionaryDocument>>> {
            const localVarAxiosArgs = await AutoCompleteApiAxiosParamCreator(configuration).apiAutoCompleteEnterpriseNameGet(keyword, size, highlight, districtId, from, color, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 行业类型
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteIndustryGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListDictionaryDocument>>> {
            const localVarAxiosArgs = await AutoCompleteApiAxiosParamCreator(configuration).apiAutoCompleteIndustryGet(keyword, size, highlight, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 猜你想找
         * @param {string} [keyword] 关键词
         * @param {number} [type] 职位&#x3D;0、公司&#x3D;1
         * @param {number} [size] 默认返回10个
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteMatchGet(keyword?: string, type?: number, size?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListPositionInputPromptEntity>>> {
            const localVarAxiosArgs = await AutoCompleteApiAxiosParamCreator(configuration).apiAutoCompleteMatchGet(keyword, type, size, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 职能类型
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompletePositionTypeGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListDictionaryDocument>>> {
            const localVarAxiosArgs = await AutoCompleteApiAxiosParamCreator(configuration).apiAutoCompletePositionTypeGet(keyword, size, highlight, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 校园名称输入提示
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {string} [color] 高亮颜色 默认#FF4400
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteSchoolNameGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListDictionaryDocument>>> {
            const localVarAxiosArgs = await AutoCompleteApiAxiosParamCreator(configuration).apiAutoCompleteSchoolNameGet(keyword, size, highlight, districtId, from, color, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * AutoCompleteApi - factory interface
 * @export
 */
export const AutoCompleteApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 企业名称输入提示
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {string} [color] 高亮颜色 默认#FF4400
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteEnterpriseNameGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
            return AutoCompleteApiFp(configuration).apiAutoCompleteEnterpriseNameGet(keyword, size, highlight, districtId, from, color, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 行业类型
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteIndustryGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
            return AutoCompleteApiFp(configuration).apiAutoCompleteIndustryGet(keyword, size, highlight, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 猜你想找
         * @param {string} [keyword] 关键词
         * @param {number} [type] 职位&#x3D;0、公司&#x3D;1
         * @param {number} [size] 默认返回10个
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteMatchGet(keyword?: string, type?: number, size?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListPositionInputPromptEntity>> {
            return AutoCompleteApiFp(configuration).apiAutoCompleteMatchGet(keyword, type, size, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 职能类型
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompletePositionTypeGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
            return AutoCompleteApiFp(configuration).apiAutoCompletePositionTypeGet(keyword, size, highlight, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 校园名称输入提示
         * @param {string} [keyword] 企业名称关键词
         * @param {number} [size] 返回多少个匹配
         * @param {number} [highlight] true&#x3D;高亮
         * @param {BussDistrict} [districtId] 请求地市
         * @param {ApplicationPlatform} [from] 请求来源
         * @param {string} [color] 高亮颜色 默认#FF4400
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiAutoCompleteSchoolNameGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
            return AutoCompleteApiFp(configuration).apiAutoCompleteSchoolNameGet(keyword, size, highlight, districtId, from, color, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * AutoCompleteApi - object-oriented interface
 * @export
 * @class AutoCompleteApi
 * @extends {BaseAPI}
 */
export class AutoCompleteApi extends BaseAPI {
    /**
     * 
     * @summary 企业名称输入提示
     * @param {string} [keyword] 企业名称关键词
     * @param {number} [size] 返回多少个匹配
     * @param {number} [highlight] true&#x3D;高亮
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {string} [color] 高亮颜色 默认#FF4400
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AutoCompleteApi
     */
    public async apiAutoCompleteEnterpriseNameGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
        return AutoCompleteApiFp(this.configuration).apiAutoCompleteEnterpriseNameGet(keyword, size, highlight, districtId, from, color, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 行业类型
     * @param {string} [keyword] 企业名称关键词
     * @param {number} [size] 返回多少个匹配
     * @param {number} [highlight] true&#x3D;高亮
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AutoCompleteApi
     */
    public async apiAutoCompleteIndustryGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
        return AutoCompleteApiFp(this.configuration).apiAutoCompleteIndustryGet(keyword, size, highlight, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 猜你想找
     * @param {string} [keyword] 关键词
     * @param {number} [type] 职位&#x3D;0、公司&#x3D;1
     * @param {number} [size] 默认返回10个
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AutoCompleteApi
     */
    public async apiAutoCompleteMatchGet(keyword?: string, type?: number, size?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListPositionInputPromptEntity>> {
        return AutoCompleteApiFp(this.configuration).apiAutoCompleteMatchGet(keyword, type, size, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 职能类型
     * @param {string} [keyword] 企业名称关键词
     * @param {number} [size] 返回多少个匹配
     * @param {number} [highlight] true&#x3D;高亮
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AutoCompleteApi
     */
    public async apiAutoCompletePositionTypeGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
        return AutoCompleteApiFp(this.configuration).apiAutoCompletePositionTypeGet(keyword, size, highlight, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 校园名称输入提示
     * @param {string} [keyword] 企业名称关键词
     * @param {number} [size] 返回多少个匹配
     * @param {number} [highlight] true&#x3D;高亮
     * @param {BussDistrict} [districtId] 请求地市
     * @param {ApplicationPlatform} [from] 请求来源
     * @param {string} [color] 高亮颜色 默认#FF4400
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AutoCompleteApi
     */
    public async apiAutoCompleteSchoolNameGet(keyword?: string, size?: number, highlight?: number, districtId?: BussDistrict, from?: ApplicationPlatform, color?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListDictionaryDocument>> {
        return AutoCompleteApiFp(this.configuration).apiAutoCompleteSchoolNameGet(keyword, size, highlight, districtId, from, color, options).then((request) => request(this.axios, this.basePath));
    }
}
