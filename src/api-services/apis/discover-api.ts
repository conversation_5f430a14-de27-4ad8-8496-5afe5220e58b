/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { RestfulResultDisoverArticleDetailDto } from '../models';
import { RestfulResultDisoverList } from '../models';
import { RestfulResultListNewsTypeDto } from '../models';
import { RestfulResultListString } from '../models';
import { RestfulResultPagedListDisoverListItemDto } from '../models';
import { RestfulResultValueTaskDiscoverOption } from '../models';
/**
 * DiscoverApi - axios parameter creator
 * @export
 */
export const DiscoverApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 文章详情
         * @param {string} [guid] 文章关键词
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDiscoverDetailGet: async (guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Discover/Detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发现频道文章
         * @param {number} [category] 0-职场风云 1-面试经 2-职业规划 3-简历制作 4-人才资讯 5-毕业生指导
         * @param {number} [page] 1
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDiscoverGetNewListGet: async (category?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Discover/GetNewList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (category !== undefined) {
                localVarQueryParameter['category'] = category;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 大家都在搜
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDiscoverHotKeywordGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Discover/HotKeyword`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary App获取发现栏目标题 0-职场风云 1-面试经 2-职业规划 3-简历制作
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDiscoverNewsTypeGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Discover/NewsType`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发现的配置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDiscoverOptionsGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Discover/Options`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发现频道文章
         * @param {string} [keyword] 文章关键词
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDiscoverSearchGet: async (keyword?: string, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/Discover/Search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DiscoverApi - functional programming interface
 * @export
 */
export const DiscoverApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 文章详情
         * @param {string} [guid] 文章关键词
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultDisoverArticleDetailDto>>> {
            const localVarAxiosArgs = await DiscoverApiAxiosParamCreator(configuration).apiDiscoverDetailGet(guid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 发现频道文章
         * @param {number} [category] 0-职场风云 1-面试经 2-职业规划 3-简历制作 4-人才资讯 5-毕业生指导
         * @param {number} [page] 1
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverGetNewListGet(category?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultDisoverList>>> {
            const localVarAxiosArgs = await DiscoverApiAxiosParamCreator(configuration).apiDiscoverGetNewListGet(category, page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 大家都在搜
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverHotKeywordGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListString>>> {
            const localVarAxiosArgs = await DiscoverApiAxiosParamCreator(configuration).apiDiscoverHotKeywordGet(districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary App获取发现栏目标题 0-职场风云 1-面试经 2-职业规划 3-简历制作
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverNewsTypeGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListNewsTypeDto>>> {
            const localVarAxiosArgs = await DiscoverApiAxiosParamCreator(configuration).apiDiscoverNewsTypeGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 发现的配置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverOptionsGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultValueTaskDiscoverOption>>> {
            const localVarAxiosArgs = await DiscoverApiAxiosParamCreator(configuration).apiDiscoverOptionsGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 发现频道文章
         * @param {string} [keyword] 文章关键词
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverSearchGet(keyword?: string, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>>> {
            const localVarAxiosArgs = await DiscoverApiAxiosParamCreator(configuration).apiDiscoverSearchGet(keyword, page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DiscoverApi - factory interface
 * @export
 */
export const DiscoverApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 文章详情
         * @param {string} [guid] 文章关键词
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultDisoverArticleDetailDto>> {
            return DiscoverApiFp(configuration).apiDiscoverDetailGet(guid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发现频道文章
         * @param {number} [category] 0-职场风云 1-面试经 2-职业规划 3-简历制作 4-人才资讯 5-毕业生指导
         * @param {number} [page] 1
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverGetNewListGet(category?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultDisoverList>> {
            return DiscoverApiFp(configuration).apiDiscoverGetNewListGet(category, page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 大家都在搜
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverHotKeywordGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListString>> {
            return DiscoverApiFp(configuration).apiDiscoverHotKeywordGet(districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary App获取发现栏目标题 0-职场风云 1-面试经 2-职业规划 3-简历制作
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverNewsTypeGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListNewsTypeDto>> {
            return DiscoverApiFp(configuration).apiDiscoverNewsTypeGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发现的配置
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverOptionsGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultValueTaskDiscoverOption>> {
            return DiscoverApiFp(configuration).apiDiscoverOptionsGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发现频道文章
         * @param {string} [keyword] 文章关键词
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDiscoverSearchGet(keyword?: string, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>> {
            return DiscoverApiFp(configuration).apiDiscoverSearchGet(keyword, page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DiscoverApi - object-oriented interface
 * @export
 * @class DiscoverApi
 * @extends {BaseAPI}
 */
export class DiscoverApi extends BaseAPI {
    /**
     * 
     * @summary 文章详情
     * @param {string} [guid] 文章关键词
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DiscoverApi
     */
    public async apiDiscoverDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultDisoverArticleDetailDto>> {
        return DiscoverApiFp(this.configuration).apiDiscoverDetailGet(guid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 发现频道文章
     * @param {number} [category] 0-职场风云 1-面试经 2-职业规划 3-简历制作 4-人才资讯 5-毕业生指导
     * @param {number} [page] 1
     * @param {number} [pageSize] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DiscoverApi
     */
    public async apiDiscoverGetNewListGet(category?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultDisoverList>> {
        return DiscoverApiFp(this.configuration).apiDiscoverGetNewListGet(category, page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 大家都在搜
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DiscoverApi
     */
    public async apiDiscoverHotKeywordGet(districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListString>> {
        return DiscoverApiFp(this.configuration).apiDiscoverHotKeywordGet(districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary App获取发现栏目标题 0-职场风云 1-面试经 2-职业规划 3-简历制作
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DiscoverApi
     */
    public async apiDiscoverNewsTypeGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListNewsTypeDto>> {
        return DiscoverApiFp(this.configuration).apiDiscoverNewsTypeGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 发现的配置
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DiscoverApi
     */
    public async apiDiscoverOptionsGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultValueTaskDiscoverOption>> {
        return DiscoverApiFp(this.configuration).apiDiscoverOptionsGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 发现频道文章
     * @param {string} [keyword] 文章关键词
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DiscoverApi
     */
    public async apiDiscoverSearchGet(keyword?: string, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>> {
        return DiscoverApiFp(this.configuration).apiDiscoverSearchGet(keyword, page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
}
