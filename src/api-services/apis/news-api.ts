/* tslint:disable */
/* eslint-disable */
/**
 * app
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { ApplicationPlatform } from '../models';
import { BussDistrict } from '../models';
import { EConsultationFromType } from '../models';
import { ESchoolO2OConsultationType } from '../models';
import { RestfulResultApplicationNoticeDto } from '../models';
import { RestfulResultArticleDetailDto } from '../models';
import { RestfulResultConsultationDetailOutput } from '../models';
import { RestfulResultGetSydwRedDot } from '../models';
import { RestfulResultListCategoryModel } from '../models';
import { RestfulResultPagedListConsultationItemOutput } from '../models';
import { RestfulResultPagedListDisoverListItemDto } from '../models';
import { RestfulResultPagedListSydwListItemDto } from '../models';
import { RestfulResultPagedListZixunListItemDto } from '../models';
import { RestfulResultSydwDto } from '../models';
import { RestfulResultZoneListDto } from '../models';
import { SydwStatus } from '../models';
/**
 * NewsApi - axios parameter creator
 * @export
 */
export const NewsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取文章列表 （数据源是主站的）
         * @param {string} [keyword] 关键词
         * @param {number} [categoryId] 类别
         * @param {BussDistrict} [districtId] 地市id
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsArticleListGet: async (keyword?: string, categoryId?: number, districtId?: BussDistrict, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/ArticleList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (categoryId !== undefined) {
                localVarQueryParameter['categoryId'] = categoryId;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取高校部bys就业服务咨询列表
         * @param {string} [keyword] \&quot;557,1604,1605,1607,1608,1609,1610,1611,1612,1613,1614,1615,1645,1670\&quot;
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsBYSJYCYArticleListGet: async (keyword?: string, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/BYSJYCYArticleList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取产业园栏目树形结构
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsCategoryForIndustrialParkGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/CategoryForIndustrialPark`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 文章详情 （数据源是主站的）
         * @param {string} [guid] 文章id
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsDetailGet: async (guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/Detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取文章列表 通用（包含子孙栏目，不含事业单位的文章.数据源是主站的）（意见反馈-常见问题则根据问题的categoryId取）
         * @param {string} [keyword] 关键词
         * @param {number} [categoryId] 类别
         * @param {BussDistrict} [districtId] 地市id
         * @param {number} [orderType] 排序类型
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {boolean} [isIncludeChild] 是否包含子孙栏目的文章
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsGetArticleCommonListGet: async (keyword?: string, categoryId?: number, districtId?: BussDistrict, orderType?: number, page?: number, pageSize?: number, isIncludeChild?: boolean, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/GetArticleCommonList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (categoryId !== undefined) {
                localVarQueryParameter['categoryId'] = categoryId;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (orderType !== undefined) {
                localVarQueryParameter['orderType'] = orderType;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (isIncludeChild !== undefined) {
                localVarQueryParameter['IsIncludeChild'] = isIncludeChild;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取校园咨询详细
         * @param {string} [guid] 
         * @param {EConsultationFromType} [fromType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsGetSchoolO2OConsultationDetailGet: async (guid?: string, fromType?: EConsultationFromType, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/GetSchoolO2OConsultationDetail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (fromType !== undefined) {
                localVarQueryParameter['fromType'] = fromType;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取校园咨询
         * @param {number} page 页码
         * @param {number} pageSize 每页记录条数
         * @param {ESchoolO2OConsultationType} [type] 咨询类型  全部 &#x3D; 0,  就业指导 &#x3D; 1,  就业创业政策 &#x3D; 2,  职位推荐 &#x3D; 3
         * @param {string} [keyWord] 搜索关键字
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsGetSchoolO2OConsultationGet: async (page: number, pageSize: number, type?: ESchoolO2OConsultationType, keyWord?: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'page' is not null or undefined
            if (page === null || page === undefined) {
                throw new RequiredError('page','Required parameter page was null or undefined when calling apiNewsGetSchoolO2OConsultationGet.');
            }
            // verify required parameter 'pageSize' is not null or undefined
            if (pageSize === null || pageSize === undefined) {
                throw new RequiredError('pageSize','Required parameter pageSize was null or undefined when calling apiNewsGetSchoolO2OConsultationGet.');
            }
            const localVarPath = `/api/News/GetSchoolO2OConsultation`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (type !== undefined) {
                localVarQueryParameter['Type'] = type;
            }

            if (keyWord !== undefined) {
                localVarQueryParameter['KeyWord'] = keyWord;
            }

            if (page !== undefined) {
                localVarQueryParameter['Page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['PageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取事业单位文章红点 （数据源是主站的）
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsGetSydwRedDotGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/GetSydwRedDot`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 文章列表 通用 （数据源是资讯中心的）
         * @param {number} [category] 
         * @param {number} [systemId] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsListGet: async (category?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/List`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (category !== undefined) {
                localVarQueryParameter['category'] = category;
            }

            if (systemId !== undefined) {
                localVarQueryParameter['SystemId'] = systemId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 网站公告(旧版/appHome/Notice)接口
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsNoticeGet: async (from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/Notice`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 文章列表 推荐 （数据源是资讯中心的）
         * @param {string} [guid] 
         * @param {number} [recommendType] 1企业、0求职者
         * @param {number} [systemId] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsRecommendListGet: async (guid?: string, recommendType?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/RecommendList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (recommendType !== undefined) {
                localVarQueryParameter['RecommendType'] = recommendType;
            }

            if (systemId !== undefined) {
                localVarQueryParameter['SystemId'] = systemId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 事业单位文章 （数据源是主站的）
         * @param {string} [guid] 文章id
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsSydwGet: async (guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/Sydw`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (guid !== undefined) {
                localVarQueryParameter['guid'] = guid;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 事业单位文章列表 （数据源是主站的）
         * @param {string} [keyword] 关键词
         * @param {SydwStatus} [state] 全部&#x3D;null，报名中 &#x3D; 1,名单公示 &#x3D; 2,事项通知 &#x3D; 3,已过期&#x3D;4,调整公告&#x3D;5
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 哪个端请求
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsSydwListGet: async (keyword?: string, state?: SydwStatus, districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/SydwList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }

            if (state !== undefined) {
                localVarQueryParameter['state'] = state;
            }

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 文章列表 （数据源是资讯中心的）
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 哪个端请求
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsZixunListGet: async (districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/ZixunList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (districtId !== undefined) {
                localVarQueryParameter['districtId'] = districtId;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 保障专区 资讯列表 （数据源是资讯中心的）
         * @param {number} [show] 0全部、1 企业、2个人
         * @param {number} [pageSize] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiNewsZoneListGet: async (show?: number, pageSize?: number, from?: ApplicationPlatform, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/News/ZoneList`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required

            if (show !== undefined) {
                localVarQueryParameter['show'] = show;
            }

            if (pageSize !== undefined) {
                localVarQueryParameter['pageSize'] = pageSize;
            }

            if (from !== undefined) {
                localVarQueryParameter['from'] = from;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * NewsApi - functional programming interface
 * @export
 */
export const NewsApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取文章列表 （数据源是主站的）
         * @param {string} [keyword] 关键词
         * @param {number} [categoryId] 类别
         * @param {BussDistrict} [districtId] 地市id
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsArticleListGet(keyword?: string, categoryId?: number, districtId?: BussDistrict, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsArticleListGet(keyword, categoryId, districtId, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取高校部bys就业服务咨询列表
         * @param {string} [keyword] \&quot;557,1604,1605,1607,1608,1609,1610,1611,1612,1613,1614,1615,1645,1670\&quot;
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsBYSJYCYArticleListGet(keyword?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsBYSJYCYArticleListGet(keyword, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取产业园栏目树形结构
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsCategoryForIndustrialParkGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultListCategoryModel>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsCategoryForIndustrialParkGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 文章详情 （数据源是主站的）
         * @param {string} [guid] 文章id
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultArticleDetailDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsDetailGet(guid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取文章列表 通用（包含子孙栏目，不含事业单位的文章.数据源是主站的）（意见反馈-常见问题则根据问题的categoryId取）
         * @param {string} [keyword] 关键词
         * @param {number} [categoryId] 类别
         * @param {BussDistrict} [districtId] 地市id
         * @param {number} [orderType] 排序类型
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {boolean} [isIncludeChild] 是否包含子孙栏目的文章
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetArticleCommonListGet(keyword?: string, categoryId?: number, districtId?: BussDistrict, orderType?: number, page?: number, pageSize?: number, isIncludeChild?: boolean, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsGetArticleCommonListGet(keyword, categoryId, districtId, orderType, page, pageSize, isIncludeChild, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取校园咨询详细
         * @param {string} [guid] 
         * @param {EConsultationFromType} [fromType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetSchoolO2OConsultationDetailGet(guid?: string, fromType?: EConsultationFromType, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultConsultationDetailOutput>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsGetSchoolO2OConsultationDetailGet(guid, fromType, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取校园咨询
         * @param {number} page 页码
         * @param {number} pageSize 每页记录条数
         * @param {ESchoolO2OConsultationType} [type] 咨询类型  全部 &#x3D; 0,  就业指导 &#x3D; 1,  就业创业政策 &#x3D; 2,  职位推荐 &#x3D; 3
         * @param {string} [keyWord] 搜索关键字
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetSchoolO2OConsultationGet(page: number, pageSize: number, type?: ESchoolO2OConsultationType, keyWord?: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListConsultationItemOutput>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsGetSchoolO2OConsultationGet(page, pageSize, type, keyWord, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取事业单位文章红点 （数据源是主站的）
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetSydwRedDotGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultGetSydwRedDot>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsGetSydwRedDotGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 文章列表 通用 （数据源是资讯中心的）
         * @param {number} [category] 
         * @param {number} [systemId] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsListGet(category?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsListGet(category, systemId, page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 网站公告(旧版/appHome/Notice)接口
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsNoticeGet(from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultApplicationNoticeDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsNoticeGet(from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 文章列表 推荐 （数据源是资讯中心的）
         * @param {string} [guid] 
         * @param {number} [recommendType] 1企业、0求职者
         * @param {number} [systemId] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsRecommendListGet(guid?: string, recommendType?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsRecommendListGet(guid, recommendType, systemId, page, pageSize, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 事业单位文章 （数据源是主站的）
         * @param {string} [guid] 文章id
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsSydwGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultSydwDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsSydwGet(guid, districtId, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 事业单位文章列表 （数据源是主站的）
         * @param {string} [keyword] 关键词
         * @param {SydwStatus} [state] 全部&#x3D;null，报名中 &#x3D; 1,名单公示 &#x3D; 2,事项通知 &#x3D; 3,已过期&#x3D;4,调整公告&#x3D;5
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 哪个端请求
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsSydwListGet(keyword?: string, state?: SydwStatus, districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsSydwListGet(keyword, state, districtId, from, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 文章列表 （数据源是资讯中心的）
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 哪个端请求
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsZixunListGet(districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultPagedListZixunListItemDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsZixunListGet(districtId, from, page, pageSize, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 保障专区 资讯列表 （数据源是资讯中心的）
         * @param {number} [show] 0全部、1 企业、2个人
         * @param {number} [pageSize] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsZoneListGet(show?: number, pageSize?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<RestfulResultZoneListDto>>> {
            const localVarAxiosArgs = await NewsApiAxiosParamCreator(configuration).apiNewsZoneListGet(show, pageSize, from, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * NewsApi - factory interface
 * @export
 */
export const NewsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取文章列表 （数据源是主站的）
         * @param {string} [keyword] 关键词
         * @param {number} [categoryId] 类别
         * @param {BussDistrict} [districtId] 地市id
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsArticleListGet(keyword?: string, categoryId?: number, districtId?: BussDistrict, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
            return NewsApiFp(configuration).apiNewsArticleListGet(keyword, categoryId, districtId, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取高校部bys就业服务咨询列表
         * @param {string} [keyword] \&quot;557,1604,1605,1607,1608,1609,1610,1611,1612,1613,1614,1615,1645,1670\&quot;
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsBYSJYCYArticleListGet(keyword?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
            return NewsApiFp(configuration).apiNewsBYSJYCYArticleListGet(keyword, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取产业园栏目树形结构
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsCategoryForIndustrialParkGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultListCategoryModel>> {
            return NewsApiFp(configuration).apiNewsCategoryForIndustrialParkGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 文章详情 （数据源是主站的）
         * @param {string} [guid] 文章id
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultArticleDetailDto>> {
            return NewsApiFp(configuration).apiNewsDetailGet(guid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取文章列表 通用（包含子孙栏目，不含事业单位的文章.数据源是主站的）（意见反馈-常见问题则根据问题的categoryId取）
         * @param {string} [keyword] 关键词
         * @param {number} [categoryId] 类别
         * @param {BussDistrict} [districtId] 地市id
         * @param {number} [orderType] 排序类型
         * @param {number} [page] 分页大小
         * @param {number} [pageSize] 页码
         * @param {boolean} [isIncludeChild] 是否包含子孙栏目的文章
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetArticleCommonListGet(keyword?: string, categoryId?: number, districtId?: BussDistrict, orderType?: number, page?: number, pageSize?: number, isIncludeChild?: boolean, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
            return NewsApiFp(configuration).apiNewsGetArticleCommonListGet(keyword, categoryId, districtId, orderType, page, pageSize, isIncludeChild, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取校园咨询详细
         * @param {string} [guid] 
         * @param {EConsultationFromType} [fromType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetSchoolO2OConsultationDetailGet(guid?: string, fromType?: EConsultationFromType, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultConsultationDetailOutput>> {
            return NewsApiFp(configuration).apiNewsGetSchoolO2OConsultationDetailGet(guid, fromType, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取校园咨询
         * @param {number} page 页码
         * @param {number} pageSize 每页记录条数
         * @param {ESchoolO2OConsultationType} [type] 咨询类型  全部 &#x3D; 0,  就业指导 &#x3D; 1,  就业创业政策 &#x3D; 2,  职位推荐 &#x3D; 3
         * @param {string} [keyWord] 搜索关键字
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetSchoolO2OConsultationGet(page: number, pageSize: number, type?: ESchoolO2OConsultationType, keyWord?: string, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListConsultationItemOutput>> {
            return NewsApiFp(configuration).apiNewsGetSchoolO2OConsultationGet(page, pageSize, type, keyWord, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取事业单位文章红点 （数据源是主站的）
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsGetSydwRedDotGet(options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultGetSydwRedDot>> {
            return NewsApiFp(configuration).apiNewsGetSydwRedDotGet(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 文章列表 通用 （数据源是资讯中心的）
         * @param {number} [category] 
         * @param {number} [systemId] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsListGet(category?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>> {
            return NewsApiFp(configuration).apiNewsListGet(category, systemId, page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 网站公告(旧版/appHome/Notice)接口
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsNoticeGet(from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultApplicationNoticeDto>> {
            return NewsApiFp(configuration).apiNewsNoticeGet(from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 文章列表 推荐 （数据源是资讯中心的）
         * @param {string} [guid] 
         * @param {number} [recommendType] 1企业、0求职者
         * @param {number} [systemId] 
         * @param {number} [page] 
         * @param {number} [pageSize] 
         * @param {BussDistrict} [districtId] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsRecommendListGet(guid?: string, recommendType?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>> {
            return NewsApiFp(configuration).apiNewsRecommendListGet(guid, recommendType, systemId, page, pageSize, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 事业单位文章 （数据源是主站的）
         * @param {string} [guid] 文章id
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 来源
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsSydwGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultSydwDto>> {
            return NewsApiFp(configuration).apiNewsSydwGet(guid, districtId, from, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 事业单位文章列表 （数据源是主站的）
         * @param {string} [keyword] 关键词
         * @param {SydwStatus} [state] 全部&#x3D;null，报名中 &#x3D; 1,名单公示 &#x3D; 2,事项通知 &#x3D; 3,已过期&#x3D;4,调整公告&#x3D;5
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 哪个端请求
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsSydwListGet(keyword?: string, state?: SydwStatus, districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
            return NewsApiFp(configuration).apiNewsSydwListGet(keyword, state, districtId, from, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 文章列表 （数据源是资讯中心的）
         * @param {BussDistrict} [districtId] 地市id
         * @param {ApplicationPlatform} [from] 哪个端请求
         * @param {number} [page] 页码
         * @param {number} [pageSize] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsZixunListGet(districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultPagedListZixunListItemDto>> {
            return NewsApiFp(configuration).apiNewsZixunListGet(districtId, from, page, pageSize, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 保障专区 资讯列表 （数据源是资讯中心的）
         * @param {number} [show] 0全部、1 企业、2个人
         * @param {number} [pageSize] 
         * @param {ApplicationPlatform} [from] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiNewsZoneListGet(show?: number, pageSize?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig): Promise<AxiosResponse<RestfulResultZoneListDto>> {
            return NewsApiFp(configuration).apiNewsZoneListGet(show, pageSize, from, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * NewsApi - object-oriented interface
 * @export
 * @class NewsApi
 * @extends {BaseAPI}
 */
export class NewsApi extends BaseAPI {
    /**
     * 
     * @summary 获取文章列表 （数据源是主站的）
     * @param {string} [keyword] 关键词
     * @param {number} [categoryId] 类别
     * @param {BussDistrict} [districtId] 地市id
     * @param {number} [page] 分页大小
     * @param {number} [pageSize] 页码
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsArticleListGet(keyword?: string, categoryId?: number, districtId?: BussDistrict, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsArticleListGet(keyword, categoryId, districtId, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取高校部bys就业服务咨询列表
     * @param {string} [keyword] \&quot;557,1604,1605,1607,1608,1609,1610,1611,1612,1613,1614,1615,1645,1670\&quot;
     * @param {number} [page] 分页大小
     * @param {number} [pageSize] 页码
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsBYSJYCYArticleListGet(keyword?: string, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsBYSJYCYArticleListGet(keyword, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取产业园栏目树形结构
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsCategoryForIndustrialParkGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultListCategoryModel>> {
        return NewsApiFp(this.configuration).apiNewsCategoryForIndustrialParkGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 文章详情 （数据源是主站的）
     * @param {string} [guid] 文章id
     * @param {BussDistrict} [districtId] 地市id
     * @param {ApplicationPlatform} [from] 来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsDetailGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultArticleDetailDto>> {
        return NewsApiFp(this.configuration).apiNewsDetailGet(guid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取文章列表 通用（包含子孙栏目，不含事业单位的文章.数据源是主站的）（意见反馈-常见问题则根据问题的categoryId取）
     * @param {string} [keyword] 关键词
     * @param {number} [categoryId] 类别
     * @param {BussDistrict} [districtId] 地市id
     * @param {number} [orderType] 排序类型
     * @param {number} [page] 分页大小
     * @param {number} [pageSize] 页码
     * @param {boolean} [isIncludeChild] 是否包含子孙栏目的文章
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsGetArticleCommonListGet(keyword?: string, categoryId?: number, districtId?: BussDistrict, orderType?: number, page?: number, pageSize?: number, isIncludeChild?: boolean, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsGetArticleCommonListGet(keyword, categoryId, districtId, orderType, page, pageSize, isIncludeChild, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取校园咨询详细
     * @param {string} [guid] 
     * @param {EConsultationFromType} [fromType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsGetSchoolO2OConsultationDetailGet(guid?: string, fromType?: EConsultationFromType, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultConsultationDetailOutput>> {
        return NewsApiFp(this.configuration).apiNewsGetSchoolO2OConsultationDetailGet(guid, fromType, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取校园咨询
     * @param {number} page 页码
     * @param {number} pageSize 每页记录条数
     * @param {ESchoolO2OConsultationType} [type] 咨询类型  全部 &#x3D; 0,  就业指导 &#x3D; 1,  就业创业政策 &#x3D; 2,  职位推荐 &#x3D; 3
     * @param {string} [keyWord] 搜索关键字
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsGetSchoolO2OConsultationGet(page: number, pageSize: number, type?: ESchoolO2OConsultationType, keyWord?: string, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListConsultationItemOutput>> {
        return NewsApiFp(this.configuration).apiNewsGetSchoolO2OConsultationGet(page, pageSize, type, keyWord, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取事业单位文章红点 （数据源是主站的）
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsGetSydwRedDotGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultGetSydwRedDot>> {
        return NewsApiFp(this.configuration).apiNewsGetSydwRedDotGet(options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 文章列表 通用 （数据源是资讯中心的）
     * @param {number} [category] 
     * @param {number} [systemId] 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsListGet(category?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsListGet(category, systemId, page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 网站公告(旧版/appHome/Notice)接口
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsNoticeGet(from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultApplicationNoticeDto>> {
        return NewsApiFp(this.configuration).apiNewsNoticeGet(from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 文章列表 推荐 （数据源是资讯中心的）
     * @param {string} [guid] 
     * @param {number} [recommendType] 1企业、0求职者
     * @param {number} [systemId] 
     * @param {number} [page] 
     * @param {number} [pageSize] 
     * @param {BussDistrict} [districtId] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsRecommendListGet(guid?: string, recommendType?: number, systemId?: number, page?: number, pageSize?: number, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListDisoverListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsRecommendListGet(guid, recommendType, systemId, page, pageSize, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 事业单位文章 （数据源是主站的）
     * @param {string} [guid] 文章id
     * @param {BussDistrict} [districtId] 地市id
     * @param {ApplicationPlatform} [from] 来源
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsSydwGet(guid?: string, districtId?: BussDistrict, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultSydwDto>> {
        return NewsApiFp(this.configuration).apiNewsSydwGet(guid, districtId, from, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 事业单位文章列表 （数据源是主站的）
     * @param {string} [keyword] 关键词
     * @param {SydwStatus} [state] 全部&#x3D;null，报名中 &#x3D; 1,名单公示 &#x3D; 2,事项通知 &#x3D; 3,已过期&#x3D;4,调整公告&#x3D;5
     * @param {BussDistrict} [districtId] 地市id
     * @param {ApplicationPlatform} [from] 哪个端请求
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsSydwListGet(keyword?: string, state?: SydwStatus, districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListSydwListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsSydwListGet(keyword, state, districtId, from, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 文章列表 （数据源是资讯中心的）
     * @param {BussDistrict} [districtId] 地市id
     * @param {ApplicationPlatform} [from] 哪个端请求
     * @param {number} [page] 页码
     * @param {number} [pageSize] 分页大小
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsZixunListGet(districtId?: BussDistrict, from?: ApplicationPlatform, page?: number, pageSize?: number, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultPagedListZixunListItemDto>> {
        return NewsApiFp(this.configuration).apiNewsZixunListGet(districtId, from, page, pageSize, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 保障专区 资讯列表 （数据源是资讯中心的）
     * @param {number} [show] 0全部、1 企业、2个人
     * @param {number} [pageSize] 
     * @param {ApplicationPlatform} [from] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof NewsApi
     */
    public async apiNewsZoneListGet(show?: number, pageSize?: number, from?: ApplicationPlatform, options?: AxiosRequestConfig) : Promise<AxiosResponse<RestfulResultZoneListDto>> {
        return NewsApiFp(this.configuration).apiNewsZoneListGet(show, pageSize, from, options).then((request) => request(this.axios, this.basePath));
    }
}
