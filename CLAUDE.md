# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发命令

### 包管理器
本项目使用 pnpm 作为包管理器：
```bash
pnpm install          # 安装依赖
pnpm dev              # 启动开发服务器 (http://localhost:3000)
pnpm build            # 构建生产版本 (包含类型检查)
pnpm preview          # 预览生产构建
pnpm type-check       # TypeScript 类型检查
pnpm lint             # ESLint 代码检查
pnpm lint:fix         # 自动修复 ESLint 问题
pnpm test             # 运行测试
pnpm test:ui          # 测试 UI 界面
```

### 开发流程
1. 修改代码前运行 `pnpm type-check` 确保类型正确
2. 提交前运行 `pnpm lint` 和 `pnpm build` 确保代码质量
3. 测试相关功能使用 `pnpm test`

## 核心架构

### 多城市门户架构
这是一个支持多城市的门户网站，采用动态路由架构：

- **路由结构**: `/:city/page` 格式，支持城市级别的页面定制
- **城市管理**: 通过 `cityStore` (Pinia) 管理城市状态和配置
- **城市代码**: 支持 `gx`(默认)、`gl`(桂林)、`nn`(南宁) 等14个城市
- **路径规则**: 广西使用根路径 `/`，其他城市使用缩写路径 `/{cityCode}`

### 状态管理 (Pinia)
- **cityStore**: 核心 store，管理当前城市、主题配置、路径生成
- **持久化**: 使用 `pinia-plugin-persistedstate` 持久化城市选择
- **主题切换**: 动态更新 CSS 变量实现主题切换

### 技术栈特性
- **Vue 3** + Composition API + TypeScript
- **Element Plus**: UI 组件库，支持自动导入
- **UnoCSS**: 原子化 CSS，配置在 `uno.config.ts`
- **Vite**: 构建工具，支持热重载和代理配置
- **自动导入**: Vue APIs 和组件通过 `unplugin-auto-import` 自动导入

## 关键文件与目录

### 路由系统 (`src/router/index.ts`)
- 动态城市路由：`/:city(gl|lz|wz|bs|...)` 支持可选城市参数
- 动态组件加载：`getCityHomePage()` 函数动态导入城市首页
- 路由守卫：城市验证和状态初始化
- 工具函数：`goToCity()`, `goToPage()` 用于城市间导航

### 城市配置 (`src/stores/cityStore.ts`)
- 完整城市配置：包含14个城市的基本信息和主题配置
- 路径生成：`getCityHomePath()`, `getCityPagePath()` 处理路径逻辑
- 状态管理：城市切换、错误处理、数据刷新

### 组件结构
- **公共组件** (`src/components/common/`): `CitySelector`, `SearchWrapper`, 
- **布局组件** (`src/components/layout/`): `HeaderComponent`, `TopBar`, `CityFooter`
- **页面视图** (`src/views/`): 包含 `homepages/{city}/` 结构的城市首页

### 配置文件
- **Vite配置** (`vite.config.ts`): 路径别名、代理配置、构建优化
- **TypeScript** (`tsconfig.json`): 严格模式，路径映射
- **ESLint** (`.eslintrc.js`): 代码规范，禁用多词组件名限制

## 开发注意事项

### 路径别名
优先使用配置的路径别名：
```typescript
import { useCityStore } from '@/stores/cityStore'
import HeaderComponent from '@/components/layout/HeaderComponent.vue'
```

### 城市首页开发
1. 新城市首页放在 `src/views/homepages/{cityCode}/HomePage.vue`
2. 在 `cityStore.ts` 的 `CITIES_CONFIG` 中添加城市配置
3. 路由会自动处理城市代码验证和组件加载

### 样式开发
- **全局样式**: `src/assets/styles/main.scss`
- **UnoCSS**: 原子化类名，配置见 `uno.config.ts`
- **主题变量**: 通过 CSS 变量实现，由 `cityStore` 动态更新

### API 配置
- 开发环境：`/api` 代理到 `http://localhost:8080`
- 环境变量：使用 `.env.development` 和 `.env.production`
- HTTP 客户端：基于 Axios，配置在 `src/utils/axios-utils.ts`

### 接口请求示例
项目使用 Swagger 自动生成的 TypeScript API 客户端，所有接口都在 `src/api-services/` 目录中。

#### 1. 基础配置
```typescript

// 获取 API 实例
import { PositionApi } from '@/api-services';
import { getPcAPI } from '@/utils/axios-utils';

const positionApi = getPcAPI(PositionApi);
```

#### 2. 职位搜索配置接口
```typescript
// GET /api/Position/SearchOption - 获取搜索配置选项
const data = await getPcAPI(PositionApi).apiPositionSearchOptionGet();
```

#### 10. 请求拦截器自动处理
- **Token 管理**: 自动添加 Bearer token 到请求头
- **Token 刷新**: 检测 token 过期并自动刷新
- **错误处理**: 统一的错误提示和 401 重定向
- **响应处理**: 标准化的响应数据格式

### 类型安全
- 城市相关类型定义在 `src/types/index.ts`
- 使用 `CityCode` 和 `CityInfo` 接口确保类型安全
- 运行 `pnpm type-check` 进行严格类型检查

### 组件命名规范
- Vue 组件：PascalCase (如 `HeaderComponent.vue`)
- Store 文件：camelCase (如 `cityStore.ts`)
- 工具函数：kebab-case (如 `storage.ts`)